"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { RefreshCw, Settings, Activity, Trash2 } from "lucide-react";
import { AuthUser } from "@/lib/auth/auth";

interface LinkedTopic {
  id: number;
  group_id: number;
  topic_id: number | null;
  group_title: string | null;
  topic_title: string | null;
  linked_by_user_id: number;
  linked_by_username: string | null;
  linked_by_first_name: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ActivityLog {
  id: number;
  topic_id: number;
  user_id: number;
  username: string | null;
  action: string;
  details: Record<string, unknown>;
  created_at: string;
  BuddyIntels_linked_topics?: {
    id: number;
    group_title: string | null;
    topic_title: string | null;
  };
}

interface TopicDashboardProps {
  initialTopics: LinkedTopic[];
  recentActivity: ActivityLog[];
  currentUser: AuthUser;
}

export function TopicDashboard({ initialTopics, recentActivity, currentUser }: TopicDashboardProps) {
  const [topics, setTopics] = useState<LinkedTopic[]>(initialTopics);
  const [activity, setActivity] = useState<ActivityLog[]>(recentActivity);
  const [loading, setLoading] = useState(false);
  const [selectedTopics, setSelectedTopics] = useState<number[]>([]);
  const [filterActive, setFilterActive] = useState<boolean | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter topics based on search and active status
  const filteredTopics = topics.filter(topic => {
    const matchesSearch = !searchTerm || 
      topic.group_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      topic.topic_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      topic.linked_by_username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      topic.linked_by_first_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterActive === null || topic.is_active === filterActive;
    
    return matchesSearch && matchesFilter;
  });

  const refreshData = async () => {
    setLoading(true);
    try {
      const [topicsResponse, activityResponse] = await Promise.all([
        fetch('/api/topics'),
        fetch('/api/topics/activity?limit=10')
      ]);

      if (topicsResponse.ok) {
        const topicsData = await topicsResponse.json();
        setTopics(topicsData.data);
      }

      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setActivity(activityData.data);
      }

      toast.success("Data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh data");
    } finally {
      setLoading(false);
    }
  };

  const toggleTopicStatus = async (topicId: number, isActive: boolean) => {
    try {
      const response = await fetch(`/api/topics/${topicId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_active: isActive,
          user_id: currentUser.id,
          username: currentUser.username,
          action_note: `${isActive ? 'Activated' : 'Deactivated'} via dashboard`
        }),
      });

      if (response.ok) {
        setTopics(prev => prev.map(topic => 
          topic.id === topicId ? { ...topic, is_active: isActive } : topic
        ));
        toast.success(`Topic ${isActive ? 'activated' : 'deactivated'} successfully`);
        refreshData(); // Refresh to get updated activity
      } else {
        throw new Error('Failed to update topic');
      }
    } catch (error) {
      console.error("Error updating topic:", error);
      toast.error("Failed to update topic status");
    }
  };

  const deleteTopic = async (topicId: number) => {
    if (!confirm("Are you sure you want to delete this topic? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`/api/topics/${topicId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: currentUser.id,
          username: currentUser.username,
          reason: 'Deleted via dashboard'
        }),
      });

      if (response.ok) {
        setTopics(prev => prev.filter(topic => topic.id !== topicId));
        toast.success("Topic deleted successfully");
        refreshData(); // Refresh to get updated activity
      } else {
        throw new Error('Failed to delete topic');
      }
    } catch (error) {
      console.error("Error deleting topic:", error);
      toast.error("Failed to delete topic");
    }
  };

  const bulkToggleStatus = async (activate: boolean) => {
    if (selectedTopics.length === 0) {
      toast.error("Please select topics to update");
      return;
    }

    try {
      const response = await fetch('/api/topics', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: activate ? 'activate' : 'deactivate',
          topic_ids: selectedTopics,
          user_id: currentUser.id,
          username: currentUser.username
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setTopics(prev => prev.map(topic => 
          selectedTopics.includes(topic.id) 
            ? { ...topic, is_active: activate } 
            : topic
        ));
        setSelectedTopics([]);
        toast.success(result.message);
        refreshData(); // Refresh to get updated activity
      } else {
        throw new Error('Failed to bulk update topics');
      }
    } catch (error) {
      console.error("Error bulk updating topics:", error);
      toast.error("Failed to bulk update topics");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'linked': return 'bg-green-100 text-green-800';
      case 'unlinked': case 'deleted': return 'bg-red-100 text-red-800';
      case 'activated': return 'bg-blue-100 text-blue-800';
      case 'deactivated': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topics.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {topics.filter(t => t.is_active).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {topics.filter(t => !t.is_active).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activity.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Topic Management</CardTitle>
          <CardDescription>
            Manage your linked Telegram topics for Twitter URL monitoring
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Topics</Label>
              <Input
                id="search"
                placeholder="Search by group, topic, or user..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex flex-col gap-2">
              <Label>Filter by Status</Label>
              <div className="flex gap-2">
                <Button
                  variant={filterActive === null ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterActive(null)}
                >
                  All
                </Button>
                <Button
                  variant={filterActive === true ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterActive(true)}
                >
                  Active
                </Button>
                <Button
                  variant={filterActive === false ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterActive(false)}
                >
                  Inactive
                </Button>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            <Button onClick={refreshData} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            
            {selectedTopics.length > 0 && (
              <>
                <Button 
                  onClick={() => bulkToggleStatus(true)} 
                  variant="outline" 
                  size="sm"
                >
                  Activate Selected ({selectedTopics.length})
                </Button>
                <Button 
                  onClick={() => bulkToggleStatus(false)} 
                  variant="outline" 
                  size="sm"
                >
                  Deactivate Selected ({selectedTopics.length})
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Topics Table */}
      <Card>
        <CardHeader>
          <CardTitle>Linked Topics ({filteredTopics.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedTopics.length === filteredTopics.length && filteredTopics.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTopics(filteredTopics.map(t => t.id));
                      } else {
                        setSelectedTopics([]);
                      }
                    }}
                  />
                </TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Linked By</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTopics.map((topic) => (
                <TableRow key={topic.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedTopics.includes(topic.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTopics(prev => [...prev, topic.id]);
                        } else {
                          setSelectedTopics(prev => prev.filter(id => id !== topic.id));
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {topic.topic_title ? (
                          <>📌 {topic.topic_title}</>
                        ) : (
                          <>🏠 {topic.group_title || 'Unknown Group'}</>
                        )}
                      </div>
                      {topic.topic_title && (
                        <div className="text-sm text-gray-500">
                          in {topic.group_title || 'Unknown Group'}
                        </div>
                      )}
                      <div className="text-xs text-gray-400">
                        Group ID: {topic.group_id}
                        {topic.topic_id && ` | Topic ID: ${topic.topic_id}`}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {topic.linked_by_first_name}
                      </div>
                      {topic.linked_by_username && (
                        <div className="text-sm text-gray-500">
                          @{topic.linked_by_username}
                        </div>
                      )}
                      <div className="text-xs text-gray-400">
                        ID: {topic.linked_by_user_id}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge
                        className={topic.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                      >
                        {topic.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                      <Switch
                        checked={topic.is_active}
                        onCheckedChange={(checked) => toggleTopicStatus(topic.id, checked)}
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(topic.created_at)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Topic Details</DialogTitle>
                            <DialogDescription>
                              Detailed information about this linked topic
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Location</Label>
                              <div className="text-sm">
                                {topic.topic_title ? (
                                  <>Topic: {topic.topic_title} in {topic.group_title}</>
                                ) : (
                                  <>Group: {topic.group_title}</>
                                )}
                              </div>
                            </div>
                            <div>
                              <Label>IDs</Label>
                              <div className="text-sm">
                                Group ID: {topic.group_id}<br />
                                {topic.topic_id && <>Topic ID: {topic.topic_id}<br /></> }
                                Internal ID: {topic.id}
                              </div>
                            </div>
                            <div>
                              <Label>Linked By</Label>
                              <div className="text-sm">
                                {topic.linked_by_first_name}
                                {topic.linked_by_username && ` (@${topic.linked_by_username})`}
                                <br />
                                User ID: {topic.linked_by_user_id}
                              </div>
                            </div>
                            <div>
                              <Label>Timestamps</Label>
                              <div className="text-sm">
                                Created: {formatDate(topic.created_at)}<br />
                                Updated: {formatDate(topic.updated_at)}
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteTopic(topic.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredTopics.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || filterActive !== null ? 'No topics match your filters' : 'No topics linked yet'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {activity.map((log) => (
              <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge className={getActionBadgeColor(log.action)}>
                    {log.action}
                  </Badge>
                  <div>
                    <div className="font-medium">
                      {log.BuddyIntels_linked_topics?.topic_title || log.BuddyIntels_linked_topics?.group_title || 'Unknown Topic'}
                    </div>
                    <div className="text-sm text-gray-500">
                      by {log.username || `User ${log.user_id}`}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-400">
                  {formatDate(log.created_at)}
                </div>
              </div>
            ))}

            {activity.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                No recent activity
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
