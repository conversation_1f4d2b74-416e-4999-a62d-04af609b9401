"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { LogOut, User, Shield, Settings } from "lucide-react";
import { AuthUser } from "@/lib/auth/auth";

interface DashboardHeaderProps {
  user: AuthUser;
}

export function DashboardHeader({ user }: DashboardHeaderProps) {
  const [loggingOut, setLoggingOut] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    setLoggingOut(true);
    
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      });

      if (response.ok) {
        toast.success("Logged out successfully");
        router.push('/login');
        router.refresh();
      } else {
        toast.error("Failed to logout");
      }
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("An error occurred during logout");
    } finally {
      setLoggingOut(false);
    }
  };

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Topic Management Dashboard
            </h1>
            <p className="text-sm text-gray-600">
              Manage your Telegram topics for Twitter URL monitoring
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* User info and permissions */}
            <div className="flex items-center gap-2">
              {user.is_admin && (
                <Badge variant="destructive" className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  Admin
                </Badge>
              )}
              
              {user.can_manage_all_topics && !user.is_admin && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Settings className="h-3 w-3" />
                  Manager
                </Badge>
              )}
              
              {user.can_link_topics && !user.can_manage_all_topics && !user.is_admin && (
                <Badge variant="outline">
                  User
                </Badge>
              )}
            </div>

            {/* User dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  {user.first_name || user.username || `User ${user.id}`}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Account</DropdownMenuLabel>
                <div className="px-2 py-1.5 text-sm text-gray-600">
                  <div>
                    {user.first_name}
                    {user.username && (
                      <span className="text-gray-400"> (@{user.username})</span>
                    )}
                  </div>
                  <div className="text-xs text-gray-400">
                    ID: {user.id}
                  </div>
                </div>
                
                <DropdownMenuSeparator />
                
                <div className="px-2 py-1.5 text-sm">
                  <div className="text-xs text-gray-500 mb-1">Permissions:</div>
                  <div className="space-y-1">
                    {user.is_admin && (
                      <div className="text-xs text-red-600">• Administrator</div>
                    )}
                    {user.can_manage_all_topics && (
                      <div className="text-xs text-blue-600">• Manage All Topics</div>
                    )}
                    {user.can_link_topics && (
                      <div className="text-xs text-green-600">• Link Topics</div>
                    )}
                  </div>
                </div>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem 
                  onClick={handleLogout}
                  disabled={loggingOut}
                  className="text-red-600 focus:text-red-600"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  {loggingOut ? 'Logging out...' : 'Logout'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
