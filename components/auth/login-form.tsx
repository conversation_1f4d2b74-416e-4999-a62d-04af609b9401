"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

export function LoginForm() {
  const [userId, setUserId] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userId.trim()) {
      toast.error("Please enter your Telegram User ID");
      return;
    }

    const userIdNum = parseInt(userId.trim());
    if (isNaN(userIdNum)) {
      toast.error("Please enter a valid numeric User ID");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userIdNum
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success("Login successful!");
        router.push('/dashboard');
        router.refresh();
      } else {
        toast.error(result.error || "Login failed");
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error("An error occurred during login");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Sign In</CardTitle>
        <CardDescription>
          Enter your Telegram User ID to access the dashboard
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="userId">Telegram User ID</Label>
            <Input
              id="userId"
              type="text"
              placeholder="e.g., 123456789"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              disabled={loading}
              required
            />
            <p className="text-sm text-gray-500">
              You can get your User ID by messaging @userinfobot on Telegram
            </p>
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </Button>
        </form>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">How to get your User ID:</h3>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Open Telegram and search for @userinfobot</li>
            <li>2. Start a chat with the bot</li>
            <li>3. Send any message to the bot</li>
            <li>4. The bot will reply with your User ID</li>
            <li>5. Copy the number and paste it above</li>
          </ol>
        </div>
        
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-medium text-yellow-900 mb-2">Access Requirements:</h3>
          <p className="text-sm text-yellow-800">
            You need to have used the bot at least once (linked a topic) or have been granted 
            permissions by an administrator to access this dashboard.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
