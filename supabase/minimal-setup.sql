  -- Minimal setup for Buddy<PERSON><PERSON><PERSON> bot
  -- Run this in Supabase SQL Editor

  -- Bot configuration table
  CREATE TABLE IF NOT EXISTS buddyintels_bot_config (
    id SERIAL PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Linked topics for dynamic topic management
  CREATE TABLE IF NOT EXISTS buddyintels_linked_topics (
    id SERIAL PRIMARY KEY,
    group_id BIGINT NOT NULL,
    topic_id BIGINT,  -- NULL for general group chat (no topics)
    group_title TEXT,
    topic_title TEXT,
    linked_by_user_id BIGINT NOT NULL,
    linked_by_username TEXT,
    linked_by_first_name TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique combination of group_id and topic_id
    UNIQUE(group_id, topic_id)
  );

  -- Error logs for debugging
  CREATE TABLE IF NOT EXISTS buddyintels_error_logs (
    id SERIAL PRIMARY KEY,
    error_type TEXT NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Insert initial configuration
  INSERT INTO buddyintels_bot_config (key, value) VALUES
    ('rate_limit_per_minute', '5'),
    ('summary_cache_hours', '24'),
    ('max_context_depth', '5'),
    ('require_admin_for_linking', 'false'),
    ('auto_detect_topic_names', 'true'),
    ('allowed_group_id', ''),
    ('allowed_topic_id', '')
  ON CONFLICT (key) DO NOTHING;