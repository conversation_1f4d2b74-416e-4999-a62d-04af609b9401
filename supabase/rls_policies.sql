-- Row Level Security (RLS) Policies for BuddyIntels Telegram Bot
-- Run this after creating the main schema to enable R<PERSON> and set up policies

-- Enable RLS on all tables
ALTER TABLE buddyintels_bot_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddyintels_linked_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddyintels_topic_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddyintels_processed_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddyintels_tweet_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddyintels_error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddyintels_topic_activity_logs ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- buddyintels_bot_config POLICIES
-- ============================================================================

-- Allow service role to read/write all config
CREATE POLICY "Service role can manage bot config" ON buddyintels_bot_config
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read config (for bot functionality)
CREATE POLICY "Authenticated users can read bot config" ON buddyintels_bot_config
  FOR SELECT USING (auth.role() = 'authenticated');

-- ============================================================================
-- buddyintels_linked_topics POLICIES
-- ============================================================================

-- Allow service role full access
CREATE POLICY "Service role can manage linked topics" ON buddyintels_linked_topics
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read all linked topics
CREATE POLICY "Authenticated users can read linked topics" ON buddyintels_linked_topics
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow users to insert topics they link themselves
CREATE POLICY "Users can link topics" ON buddyintels_linked_topics
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    linked_by_user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT
  );

-- Allow users to update topics they linked or if they have admin permissions
CREATE POLICY "Users can update their linked topics or admins can update all" ON buddyintels_linked_topics
  FOR UPDATE USING (
    auth.role() = 'authenticated' AND (
      linked_by_user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT OR
      EXISTS (
        SELECT 1 FROM buddyintels_topic_permissions tp
        WHERE tp.user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT
        AND (tp.is_admin = TRUE OR tp.can_manage_all_topics = TRUE)
      )
    )
  );

-- ============================================================================
-- buddyintels_topic_permissions POLICIES
-- ============================================================================

-- Allow service role full access
CREATE POLICY "Service role can manage topic permissions" ON buddyintels_topic_permissions
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read all permissions (for permission checks)
CREATE POLICY "Authenticated users can read topic permissions" ON buddyintels_topic_permissions
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow admins to insert/update permissions
CREATE POLICY "Admins can manage topic permissions" ON buddyintels_topic_permissions
  FOR ALL USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM buddyintels_topic_permissions tp
      WHERE tp.user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT
      AND tp.is_admin = TRUE
    )
  );

-- ============================================================================
-- buddyintels_processed_messages POLICIES
-- ============================================================================

-- Allow service role full access
CREATE POLICY "Service role can manage processed messages" ON buddyintels_processed_messages
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read processed messages
CREATE POLICY "Authenticated users can read processed messages" ON buddyintels_processed_messages
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow users to insert their own processed messages
CREATE POLICY "Users can insert their processed messages" ON buddyintels_processed_messages
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT
  );

-- ============================================================================
-- buddyintels_tweet_summaries POLICIES
-- ============================================================================

-- Allow service role full access
CREATE POLICY "Service role can manage tweet summaries" ON buddyintels_tweet_summaries
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read tweet summaries
CREATE POLICY "Authenticated users can read tweet summaries" ON buddyintels_tweet_summaries
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert tweet summaries
CREATE POLICY "Authenticated users can insert tweet summaries" ON buddyintels_tweet_summaries
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update tweet summaries
CREATE POLICY "Authenticated users can update tweet summaries" ON BuddyIntels_tweet_summaries
  FOR UPDATE USING (auth.role() = 'authenticated');

-- ============================================================================
-- buddyintels_error_logs POLICIES
-- ============================================================================

-- Allow service role full access
CREATE POLICY "Service role can manage error logs" ON buddyintels_error_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read error logs (for debugging)
CREATE POLICY "Authenticated users can read error logs" ON buddyintels_error_logs
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert error logs
CREATE POLICY "Authenticated users can insert error logs" ON buddyintels_error_logs
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- ============================================================================
-- buddyintels_topic_activity_logs POLICIES
-- ============================================================================

-- Allow service role full access
CREATE POLICY "Service role can manage topic activity logs" ON buddyintels_topic_activity_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to read activity logs
CREATE POLICY "Authenticated users can read topic activity logs" ON buddyintels_topic_activity_logs
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow users to insert activity logs for their own actions
CREATE POLICY "Users can insert their topic activity logs" ON buddyintels_topic_activity_logs
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT
  );

-- ============================================================================
-- HELPER FUNCTIONS FOR RLS
-- ============================================================================

-- Function to check if current user is admin
CREATE OR REPLACE FUNCTION is_current_user_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM BuddyIntels_topic_permissions tp
    WHERE tp.user_id = (auth.jwt() ->> 'telegram_user_id')::BIGINT
    AND tp.is_admin = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current telegram user id from JWT
CREATE OR REPLACE FUNCTION get_current_telegram_user_id()
RETURNS BIGINT AS $$
BEGIN
  RETURN (auth.jwt() ->> 'telegram_user_id')::BIGINT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant usage on schema to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;

-- Grant permissions on tables to authenticated users
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT INSERT ON BuddyIntels_linked_topics TO authenticated;
GRANT UPDATE ON BuddyIntels_linked_topics TO authenticated;
GRANT INSERT ON BuddyIntels_processed_messages TO authenticated;
GRANT INSERT, UPDATE ON BuddyIntels_tweet_summaries TO authenticated;
GRANT INSERT ON BuddyIntels_error_logs TO authenticated;
GRANT INSERT ON BuddyIntels_topic_activity_logs TO authenticated;

-- Grant all permissions to service role
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION cleanup_expired_summaries() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION log_error(TEXT, TEXT, TEXT, JSONB) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION log_topic_activity(INTEGER, BIGINT, TEXT, TEXT, JSONB) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_active_linked_topics() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION can_user_manage_topics(BIGINT) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION is_current_user_admin() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_current_telegram_user_id() TO authenticated, service_role;

-- ============================================================================
-- NOTES AND IMPLEMENTATION GUIDE
-- ============================================================================

/*
IMPORTANT NOTES FOR IMPLEMENTATION:

1. JWT Token Structure:
   Your JWT tokens should include a 'telegram_user_id' claim that contains
   the Telegram user ID as a string that can be cast to BIGINT.

   Example JWT payload:
   {
     "aud": "authenticated",
     "exp": 1234567890,
     "sub": "user-uuid",
     "telegram_user_id": "123456789",
     "role": "authenticated"
   }

2. Authentication Flow:
   - Use 'service_role' for server-side operations (bot operations)
   - Use 'authenticated' role for user-specific operations
   - The policies assume telegram_user_id is available in the JWT

3. Admin Setup:
   To set up initial admins, use the service role to insert records into
   BuddyIntels_topic_permissions with is_admin = TRUE:

   INSERT INTO BuddyIntels_topic_permissions (user_id, username, first_name, is_admin)
   VALUES (123456789, 'admin_username', 'Admin Name', TRUE);

4. Security Considerations:
   - Service role has full access and should only be used server-side
   - All user operations go through RLS policies
   - Users can only modify data they own or have explicit permissions for
   - Bot operations should use service_role for unrestricted access
   - User-facing operations should use authenticated role with proper JWT

5. Testing RLS:
   You can test policies by setting the role and JWT claims:

   -- Test as service role (full access)
   SET ROLE service_role;
   SELECT * FROM BuddyIntels_linked_topics;

   -- Test as authenticated user
   SET ROLE authenticated;
   -- You'll need to set JWT claims for proper testing
   RESET ROLE;

6. Common Operations:

   a) Bot linking a topic (service role):
      Uses service_role, bypasses RLS

   b) User checking their permissions:
      Uses authenticated role, can read their own permissions

   c) Admin managing permissions:
      Uses authenticated role, admin can modify all permissions

   d) User viewing linked topics:
      Uses authenticated role, can read all active topics

7. Error Handling:
   - If JWT doesn't contain telegram_user_id, operations will fail
   - Ensure proper error handling in your application
   - Use the log_error function for debugging RLS issues

8. Performance Notes:
   - RLS policies add overhead to queries
   - Consider using service_role for bulk operations
   - Monitor query performance and add indexes as needed
   - The existing indexes should handle most common query patterns

9. Maintenance:
   - Regularly clean up expired tweet summaries using cleanup_expired_summaries()
   - Monitor error logs for RLS policy violations
   - Review and update permissions as needed
*/
