-- Command Analytics and Usage Tracking for BuddyIntels Bot
-- Run this after the main schema to add command tracking capabilities

-- ============================================================================
-- COMMAND USAGE ANALYTICS TABLE
-- ============================================================================

-- Track all bot command usage for analytics and debugging
CREATE TABLE IF NOT EXISTS buddyintels_command_usage (
  id SERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  username TEXT,
  first_name TEXT,
  last_name TEXT,
  chat_id BIGINT NOT NULL,
  chat_type TEXT NOT NULL, -- 'private', 'group', 'supergroup', 'channel'
  chat_title TEXT,
  topic_id BIGINT, -- NULL for non-topic messages
  topic_title TEXT,
  command TEXT NOT NULL, -- e.g., 'start', 'help', 'link', 'status'
  command_args TEXT, -- Any arguments passed with the command
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT, -- If success = false, store error details
  response_time_ms INTEGER, -- How long the command took to process
  rate_limited BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- COMMAND STATISTICS VIEW
-- ============================================================================

-- Create a view for easy command statistics
CREATE OR REPLACE VIEW buddyintels_command_stats AS
SELECT 
  command,
  COUNT(*) as total_uses,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(DISTINCT chat_id) as unique_chats,
  AVG(response_time_ms) as avg_response_time_ms,
  COUNT(CASE WHEN success = false THEN 1 END) as error_count,
  COUNT(CASE WHEN rate_limited = true THEN 1 END) as rate_limited_count,
  MAX(created_at) as last_used,
  DATE_TRUNC('day', created_at) as date
FROM buddyintels_command_usage
GROUP BY command, DATE_TRUNC('day', created_at)
ORDER BY date DESC, total_uses DESC;

-- ============================================================================
-- USER ACTIVITY ANALYTICS
-- ============================================================================

-- Track user activity patterns
CREATE OR REPLACE VIEW buddyintels_user_activity AS
SELECT 
  user_id,
  username,
  first_name,
  COUNT(*) as total_commands,
  COUNT(DISTINCT command) as unique_commands_used,
  COUNT(DISTINCT chat_id) as chats_active_in,
  AVG(response_time_ms) as avg_response_time,
  COUNT(CASE WHEN success = false THEN 1 END) as error_count,
  COUNT(CASE WHEN rate_limited = true THEN 1 END) as rate_limited_count,
  MIN(created_at) as first_seen,
  MAX(created_at) as last_seen,
  DATE_TRUNC('day', MAX(created_at)) as last_active_date
FROM buddyintels_command_usage
GROUP BY user_id, username, first_name
ORDER BY total_commands DESC;

-- ============================================================================
-- CHAT ACTIVITY ANALYTICS
-- ============================================================================

-- Track chat/group activity
CREATE OR REPLACE VIEW BuddyIntels_chat_activity AS
SELECT 
  chat_id,
  chat_type,
  chat_title,
  COUNT(*) as total_commands,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(DISTINCT command) as unique_commands_used,
  AVG(response_time_ms) as avg_response_time,
  COUNT(CASE WHEN success = false THEN 1 END) as error_count,
  MIN(created_at) as first_activity,
  MAX(created_at) as last_activity
FROM buddyintels_command_usage
GROUP BY chat_id, chat_type, chat_title
ORDER BY total_commands DESC;

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to log command usage
CREATE OR REPLACE FUNCTION log_command_usage(
  user_id_param BIGINT,
  username_param TEXT DEFAULT NULL,
  first_name_param TEXT DEFAULT NULL,
  last_name_param TEXT DEFAULT NULL,
  chat_id_param BIGINT,
  chat_type_param TEXT,
  chat_title_param TEXT DEFAULT NULL,
  topic_id_param BIGINT DEFAULT NULL,
  topic_title_param TEXT DEFAULT NULL,
  command_param TEXT,
  command_args_param TEXT DEFAULT NULL,
  success_param BOOLEAN DEFAULT TRUE,
  error_message_param TEXT DEFAULT NULL,
  response_time_ms_param INTEGER DEFAULT NULL,
  rate_limited_param BOOLEAN DEFAULT FALSE
)
RETURNS void AS $$
BEGIN
  INSERT INTO buddyintels_command_usage (
    user_id, username, first_name, last_name,
    chat_id, chat_type, chat_title,
    topic_id, topic_title,
    command, command_args,
    success, error_message, response_time_ms, rate_limited
  ) VALUES (
    user_id_param, username_param, first_name_param, last_name_param,
    chat_id_param, chat_type_param, chat_title_param,
    topic_id_param, topic_title_param,
    command_param, command_args_param,
    success_param, error_message_param, response_time_ms_param, rate_limited_param
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get command usage statistics for a specific period
CREATE OR REPLACE FUNCTION get_command_stats(
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '7 days',
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
  command TEXT,
  total_uses BIGINT,
  unique_users BIGINT,
  unique_chats BIGINT,
  avg_response_time_ms NUMERIC,
  error_rate NUMERIC,
  rate_limit_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cu.command,
    COUNT(*) as total_uses,
    COUNT(DISTINCT cu.user_id) as unique_users,
    COUNT(DISTINCT cu.chat_id) as unique_chats,
    AVG(cu.response_time_ms) as avg_response_time_ms,
    (COUNT(CASE WHEN cu.success = false THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC * 100) as error_rate,
    (COUNT(CASE WHEN cu.rate_limited = true THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC * 100) as rate_limit_rate
  FROM buddyintels_command_usage cu
  WHERE cu.created_at >= start_date AND cu.created_at <= end_date
  GROUP BY cu.command
  ORDER BY total_uses DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get top users by activity
CREATE OR REPLACE FUNCTION get_top_users(
  limit_count INTEGER DEFAULT 10,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days'
)
RETURNS TABLE (
  user_id BIGINT,
  username TEXT,
  first_name TEXT,
  total_commands BIGINT,
  unique_commands BIGINT,
  last_activity TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cu.user_id,
    cu.username,
    cu.first_name,
    COUNT(*) as total_commands,
    COUNT(DISTINCT cu.command) as unique_commands,
    MAX(cu.created_at) as last_activity
  FROM buddyintels_command_usage cu
  WHERE cu.created_at >= start_date
  GROUP BY cu.user_id, cu.username, cu.first_name
  ORDER BY total_commands DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old command usage data
CREATE OR REPLACE FUNCTION cleanup_old_command_usage(
  days_to_keep INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM buddyintels_command_usage 
  WHERE created_at < NOW() - (days_to_keep || ' days')::INTERVAL;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_command_usage_user_id ON buddyintels_command_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_command_usage_chat_id ON buddyintels_command_usage(chat_id);
CREATE INDEX IF NOT EXISTS idx_command_usage_command ON buddyintels_command_usage(command);
CREATE INDEX IF NOT EXISTS idx_command_usage_created_at ON buddyintels_command_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_command_usage_success ON buddyintels_command_usage(success);
CREATE INDEX IF NOT EXISTS idx_command_usage_rate_limited ON buddyintels_command_usage(rate_limited);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_command_usage_user_command ON buddyintels_command_usage(user_id, command);
CREATE INDEX IF NOT EXISTS idx_command_usage_chat_command ON buddyintels_command_usage(chat_id, command);
CREATE INDEX IF NOT EXISTS idx_command_usage_date_command ON buddyintels_command_usage(DATE_TRUNC('day', created_at), command);

-- ============================================================================
-- SAMPLE QUERIES FOR ANALYTICS
-- ============================================================================

/*
-- Get command usage for the last 7 days
SELECT * FROM get_command_stats();

-- Get top 10 most active users in the last 30 days
SELECT * FROM get_top_users(10);

-- Get daily command usage for a specific command
SELECT 
  DATE_TRUNC('day', created_at) as date,
  COUNT(*) as uses
FROM buddyintels_command_usage 
WHERE command = 'help' 
  AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date;

-- Get error rate by command
SELECT 
  command,
  COUNT(*) as total,
  COUNT(CASE WHEN success = false THEN 1 END) as errors,
  (COUNT(CASE WHEN success = false THEN 1 END)::FLOAT / COUNT(*) * 100) as error_rate
FROM buddyintels_command_usage
GROUP BY command
ORDER BY error_rate DESC;

-- Clean up old data (keep last 90 days)
SELECT cleanup_old_command_usage(90);
*/
