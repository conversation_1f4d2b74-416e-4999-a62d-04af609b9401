-- Migration: Add topic_type column to buddyintels_linked_topics
-- This enables LOGS topics with simplified message formatting

-- Add topic_type column with default value 'standard'
ALTER TABLE buddyintels_linked_topics 
ADD COLUMN IF NOT EXISTS topic_type VARCHAR(20) DEFAULT 'standard' 
CHECK (topic_type IN ('standard', 'logs'));

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_linked_topics_topic_type ON buddyintels_linked_topics(topic_type);

-- Add comment to document the feature
COMMENT ON COLUMN buddyintels_linked_topics.topic_type IS 'Type of topic: standard (full summary) or logs (simple DD/MM/YYYY | Headline | Link format)';

-- Update function to include topic_type in active topics query
CREATE OR REPLACE FUNCTION get_active_linked_topics()
RETURNS TABLE (
  id INTEGER,
  group_id BIGINT,
  topic_id BIGINT,
  group_title TEXT,
  topic_title TEXT,
  topic_type VARCHAR(20)
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    lt.id,
    lt.group_id,
    lt.topic_id,
    lt.group_title,
    lt.topic_title,
    lt.topic_type
  FROM buddyintels_linked_topics lt
  WHERE lt.is_active = TRUE
  ORDER BY lt.created_at DESC;
END;
$$ LANGUAGE plpgsql;