import PQueue from 'p-queue';

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

export class RateLimiter {
  private limits = new Map<string, RateLimitEntry>();
  private queue = new PQueue({ concurrency: 1 });

  async checkRateLimit(userId: string, limitPerMinute: number): Promise<boolean> {
    const now = Date.now();
    const key = `user:${userId}`;
    
    const result = await this.queue.add(async () => {
      const entry = this.limits.get(key);
      
      if (!entry) {
        this.limits.set(key, { count: 1, resetTime: now + 60000 });
        return true;
      }
      
      if (now > entry.resetTime) {
        this.limits.set(key, { count: 1, resetTime: now + 60000 });
        return true;
      }
      
      if (entry.count >= limitPerMinute) {
        return false;
      }
      
      entry.count++;
      return true;
    });
    
    return result ?? false;
  }

  async getRemainingRequests(userId: string, limitPerMinute: number): Promise<number> {
    const key = `user:${userId}`;
    const entry = this.limits.get(key);
    
    if (!entry) return limitPerMinute;
    
    const now = Date.now();
    if (now > entry.resetTime) return limitPerMinute;
    
    return Math.max(0, limitPerMinute - entry.count);
  }

  async getResetTime(userId: string): Promise<number> {
    const key = `user:${userId}`;
    const entry = this.limits.get(key);
    
    if (!entry) return 0;
    
    return Math.max(0, entry.resetTime - Date.now());
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.limits.entries()) {
      if (now > entry.resetTime) {
        this.limits.delete(key);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();

// Clean up expired entries every 5 minutes
setInterval(() => {
  rateLimiter.cleanup();
}, 5 * 60 * 1000);