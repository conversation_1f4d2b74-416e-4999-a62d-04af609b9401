import { createBotClient } from '@/lib/supabase/bot-client';

export interface AdminUser {
  id: number;
  user_id: number;
  username: string | null;
  first_name: string | null;
  last_name: string | null;
  is_active: boolean;
  added_by_user_id: number | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

export class AdminManager {
  /**
   * Check if a user is an admin
   */
  async isAdmin(userId: number): Promise<boolean> {
    try {
      const supabase = createBotClient();
      
      const { data, error } = await supabase
        .from('buddyintels_admins')
        .select('user_id, is_active')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        console.log(`[AdminManager] User ${userId} is not an admin`);
        return false;
      }

      console.log(`[AdminManager] User ${userId} is an admin`);
      return true;
    } catch (error) {
      console.error(`[AdminManager] Error checking admin status for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Add a new admin user
   */
  async addAdmin(
    userId: number,
    addedByUserId: number,
    username?: string,
    firstName?: string,
    lastName?: string,
    notes?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if the user adding is an admin
      const isAdderAdmin = await this.isAdmin(addedByUserId);
      if (!isAdderAdmin) {
        return { success: false, message: 'Only admins can add new admins.' };
      }

      const supabase = createBotClient();
      
      const { error } = await supabase
        .from('buddyintels_admins')
        .insert({
          user_id: userId,
          username: username,
          first_name: firstName,
          last_name: lastName,
          added_by_user_id: addedByUserId,
          notes: notes || `Added by admin ${addedByUserId}`,
          is_active: true
        });

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          return { success: false, message: 'User is already an admin.' };
        }
        throw error;
      }

      console.log(`[AdminManager] Added new admin: ${userId} by ${addedByUserId}`);
      return { success: true, message: `Successfully added user ${userId} as admin.` };
    } catch (error) {
      console.error(`[AdminManager] Error adding admin ${userId}:`, error);
      return { success: false, message: 'Failed to add admin. Please try again.' };
    }
  }

  /**
   * Remove an admin user (deactivate)
   */
  async removeAdmin(
    userId: number,
    removedByUserId: number
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if the user removing is an admin
      const isRemoverAdmin = await this.isAdmin(removedByUserId);
      if (!isRemoverAdmin) {
        return { success: false, message: 'Only admins can remove other admins.' };
      }

      // Prevent self-removal
      if (userId === removedByUserId) {
        return { success: false, message: 'You cannot remove yourself as admin.' };
      }

      const supabase = createBotClient();
      
      const { error } = await supabase
        .from('buddyintels_admins')
        .update({ 
          is_active: false, 
          updated_at: new Date().toISOString() 
        })
        .eq('user_id', userId);

      if (error) throw error;

      console.log(`[AdminManager] Removed admin: ${userId} by ${removedByUserId}`);
      return { success: true, message: `Successfully removed user ${userId} as admin.` };
    } catch (error) {
      console.error(`[AdminManager] Error removing admin ${userId}:`, error);
      return { success: false, message: 'Failed to remove admin. Please try again.' };
    }
  }

  /**
   * List all active admins
   */
  async listAdmins(): Promise<AdminUser[]> {
    try {
      const supabase = createBotClient();
      
      const { data, error } = await supabase
        .from('buddyintels_admins')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: true });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('[AdminManager] Error listing admins:', error);
      return [];
    }
  }

  /**
   * Check if admin access is required for all commands
   */
  async isAdminRequiredForCommands(): Promise<boolean> {
    try {
      const supabase = createBotClient();
      
      const { data, error } = await supabase
        .from('buddyintels_bot_config')
        .select('value')
        .eq('key', 'require_admin_for_all_commands')
        .single();

      if (error || !data) {
        return false; // Default to false if config not found
      }

      return data.value === 'true';
    } catch (error) {
      console.error('[AdminManager] Error checking admin requirement:', error);
      return false;
    }
  }
}

export const adminManager = new AdminManager();
