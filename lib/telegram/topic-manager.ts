import { createBotClient } from '@/lib/supabase/bot-client';
import { topicLogger } from '@/lib/utils/logger';

export interface LinkedTopic {
  id: number;
  group_id: number;
  topic_id: number | null;
  group_title: string | null;
  topic_title: string | null;
  linked_by_user_id: number;
  linked_by_username: string | null;
  linked_by_first_name: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  topic_type: 'standard' | 'logs';
}

export interface TopicPermission {
  id: number;
  user_id: number;
  username: string | null;
  first_name: string | null;
  is_admin: boolean;
  can_link_topics: boolean;
  can_manage_all_topics: boolean;
  created_at: string;
  updated_at: string;
}

export class TopicManager {
  
  /**
   * Check if a user has permission to link topics
   */
  async canUserLinkTopics(userId: number): Promise<boolean> {
    topicLogger.debug(`Checking link permissions for user ${userId}`, { user_id: userId });

    try {
      const supabase = createBotClient();
      
      // Check if user has explicit permissions
      const { data: permission } = await supabase
        .from('buddyintels_topic_permissions')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (permission) {
        const canLink = permission.is_admin || permission.can_link_topics;
        console.log(`[TopicManager] User ${userId} has explicit permissions: canLink=${canLink}`);
        return canLink;
      }

      // Check if linking is open to all users
      const { data: config } = await supabase
        .from('buddyintels_bot_config')
        .select('value')
        .eq('key', 'require_admin_for_linking')
        .single();

      const requireAdmin = config?.value === 'true';
      const canLink = !requireAdmin;
      console.log(`[TopicManager] No explicit permissions for user ${userId}, require_admin=${requireAdmin}, canLink=${canLink}`);
      
      return canLink;
    } catch (error) {
      console.error(`[TopicManager] Error checking permissions for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Link a topic for monitoring
   */
  async linkTopic(
    groupId: number,
    topicId: number | null,
    groupTitle: string | null,
    topicTitle: string | null,
    userId: number,
    username: string | null,
    firstName: string | null,
    topicType: 'standard' | 'logs' = 'standard'
  ): Promise<{ success: boolean; message: string; topicData?: LinkedTopic }> {
    console.log(`[TopicManager] Attempting to link topic: groupId=${groupId}, topicId=${topicId}, userId=${userId}`);

    try {
      // Check permissions
      const canLink = await this.canUserLinkTopics(userId);
      if (!canLink) {
        console.log(`[TopicManager] User ${userId} does not have permission to link topics`);
        return { success: false, message: 'You do not have permission to link topics.' };
      }

      const supabase = createBotClient();

      // Check if topic is already linked
      const { data: existing } = await supabase
        .from('buddyintels_linked_topics')
        .select('*')
        .eq('group_id', groupId)
        .eq('topic_id', topicId)
        .single();

      if (existing) {
        if (existing.is_active) {
          console.log(`[TopicManager] Topic already linked and active: groupId=${groupId}, topicId=${topicId}`);
          return { success: false, message: 'This topic is already linked and active.' };
        } else {
          // Reactivate existing topic
          const { data: updated, error } = await supabase
            .from('buddyintels_linked_topics')
            .update({
              is_active: true,
              updated_at: new Date().toISOString(),
              group_title: groupTitle,
              topic_title: topicTitle
            })
            .eq('id', existing.id)
            .select()
            .single();

          if (error) throw error;

          // Log activity
          await this.logActivity(existing.id, userId, username, 'reactivated', {
            group_title: groupTitle,
            topic_title: topicTitle
          });

          console.log(`[TopicManager] Reactivated existing topic: id=${existing.id}`);
          return { 
            success: true, 
            message: 'Topic has been reactivated for monitoring.',
            topicData: updated
          };
        }
      }

      // Create new linked topic
      const { data: newTopic, error } = await supabase
        .from('buddyintels_linked_topics')
        .insert({
          group_id: groupId,
          topic_id: topicId,
          group_title: groupTitle,
          topic_title: topicTitle,
          linked_by_user_id: userId,
          linked_by_username: username,
          linked_by_first_name: firstName,
          is_active: true,
          topic_type: topicType
        })
        .select()
        .single();

      if (error) throw error;

      // Log activity
      await this.logActivity(newTopic.id, userId, username, 'linked', {
        group_title: groupTitle,
        topic_title: topicTitle
      });

      console.log(`[TopicManager] Successfully linked new topic: id=${newTopic.id}`);
      return { 
        success: true, 
        message: 'Topic has been successfully linked for monitoring!',
        topicData: newTopic
      };

    } catch (error) {
      console.error(`[TopicManager] Error linking topic:`, error);
      
      // Determine specific error message
      let userMessage = 'An error occurred while linking the topic. Please try again.';
      let errorType = 'link_command_error';
      
      if (error instanceof Error) {
        // Database connection issues
        if (error.message.includes('relation') && error.message.includes('does not exist')) {
          userMessage = 'Database tables are not set up. Please contact the administrator.';
          errorType = 'database_schema_error';
        }
        // Connection timeout or network issues
        else if (error.message.includes('timeout') || error.message.includes('ENOTFOUND')) {
          userMessage = 'Database connection failed. Please try again later.';
          errorType = 'database_connection_error';
        }
        // Permission issues
        else if (error.message.includes('permission') || error.message.includes('access')) {
          userMessage = 'Database access denied. Please contact the administrator.';
          errorType = 'database_permission_error';
        }
        // Constraint violations (duplicate entries)
        else if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
          userMessage = 'This topic is already linked. Try /unlink first if you want to re-link it.';
          errorType = 'duplicate_link_error';
        }
      }
      
      // Log detailed error to database (if possible)
      try {
        const supabase = createBotClient();
        await supabase.from('buddyintels_error_logs').insert({
          error_type: errorType,
          error_message: error instanceof Error ? error.message : String(error),
          stack_trace: error instanceof Error ? error.stack : null,
          context: JSON.stringify({ 
            groupId, 
            topicId, 
            userId, 
            username, 
            firstName,
            timestamp: new Date().toISOString(),
            action: 'link_topic'
          })
        });
      } catch (logError) {
        console.error('[TopicManager] Failed to log error to database:', logError);
        // Fallback: log to console with structured format
        console.error('[TopicManager] ERROR_LOG:', {
          type: errorType,
          message: error instanceof Error ? error.message : String(error),
          context: { groupId, topicId, userId, username, firstName },
          timestamp: new Date().toISOString()
        });
      }
      
      return { 
        success: false, 
        message: userMessage
      };
    }
  }

  /**
   * Get all active linked topics
   */
  async getActiveTopics(): Promise<LinkedTopic[]> {
    console.log(`[TopicManager] Fetching all active topics`);
    
    try {
      const supabase = createBotClient();
      const { data, error } = await supabase
        .from('buddyintels_linked_topics')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log(`[TopicManager] Found ${data?.length || 0} active topics`);
      return data || [];
    } catch (error) {
      console.error(`[TopicManager] Error fetching active topics:`, error);
      return [];
    }
  }

  /**
   * Check if a message is from an allowed topic
   */
  async isMessageFromAllowedTopic(groupId: number, topicId: number | null): Promise<boolean> {
    try {
      const activeTopics = await this.getActiveTopics();
      
      const isAllowed = activeTopics.some(topic => 
        topic.group_id === groupId && topic.topic_id === topicId
      );

      console.log(`[TopicManager] Message from groupId=${groupId}, topicId=${topicId} is ${isAllowed ? 'allowed' : 'not allowed'}`);
      return isAllowed;
    } catch (error) {
      console.error(`[TopicManager] Error checking if message is allowed:`, error);
      return false;
    }
  }

  /**
   * Log topic activity
   */
  private async logActivity(
    topicId: number,
    userId: number,
    username: string | null,
    action: string,
    details?: Record<string, unknown>
  ): Promise<void> {
    try {
      const supabase = createBotClient();
      await supabase
        .from('buddyintels_topic_activity_logs')
        .insert({
          topic_id: topicId,
          user_id: userId,
          username: username,
          action: action,
          details: details || {}
        });
      
      console.log(`[TopicManager] Logged activity: topicId=${topicId}, action=${action}, userId=${userId}`);
    } catch (error) {
      console.error(`[TopicManager] Error logging activity:`, error);
    }
  }

  /**
   * Unlink a topic
   */
  async unlinkTopic(
    groupId: number,
    topicId: number | null,
    userId: number,
    username: string | null
  ): Promise<{ success: boolean; message: string }> {
    console.log(`[TopicManager] Attempting to unlink topic: groupId=${groupId}, topicId=${topicId}, userId=${userId}`);

    try {
      const supabase = createBotClient();

      // Find the topic
      const { data: topic } = await supabase
        .from('buddyintels_linked_topics')
        .select('*')
        .eq('group_id', groupId)
        .eq('topic_id', topicId)
        .eq('is_active', true)
        .single();

      if (!topic) {
        return { success: false, message: 'This topic is not currently linked.' };
      }

      // Check if user can unlink (must be the one who linked it, or have admin permissions)
      const canManage = await this.canUserManageTopic(userId, topic.linked_by_user_id);
      if (!canManage) {
        return { success: false, message: 'You do not have permission to unlink this topic.' };
      }

      // Deactivate the topic
      const { error } = await supabase
        .from('buddyintels_linked_topics')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', topic.id);

      if (error) throw error;

      // Log activity
      await this.logActivity(topic.id, userId, username, 'unlinked');

      console.log(`[TopicManager] Successfully unlinked topic: id=${topic.id}`);
      return { success: true, message: 'Topic has been unlinked successfully.' };

    } catch (error) {
      console.error(`[TopicManager] Error unlinking topic:`, error);
      return { success: false, message: 'An error occurred while unlinking the topic.' };
    }
  }

  /**
   * Get topic information including type
   */
  async getTopicInfo(groupId: number, topicId: number | null): Promise<LinkedTopic | null> {
    try {
      const supabase = createBotClient();
      const { data: topic } = await supabase
        .from('buddyintels_linked_topics')
        .select('*')
        .eq('group_id', groupId)
        .eq('topic_id', topicId)
        .eq('is_active', true)
        .single();

      return topic || null;
    } catch (error) {
      console.error(`[TopicManager] Error fetching topic info:`, error);
      return null;
    }
  }

  /**
   * Get all active logs topics
   */
  async getLogsTopics(): Promise<LinkedTopic[]> {
    try {
      const supabase = createBotClient();

      const { data: topics, error } = await supabase
        .from('buddyintels_linked_topics')
        .select('*')
        .eq('topic_type', 'logs')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('[TopicManager] Error fetching logs topics:', error);
        return [];
      }

      console.log(`[TopicManager] Found ${topics?.length || 0} active logs topics`);
      return topics || [];
    } catch (error) {
      console.error('[TopicManager] Error in getLogsTopics:', error);
      return [];
    }
  }

  /**
   * Set/change topic type
   */
  async setTopicType(
    groupId: number,
    topicId: number | null,
    newType: 'standard' | 'logs',
    userId: number,
    username: string | null
  ): Promise<{ success: boolean; message: string }> {
    console.log(`[TopicManager] Attempting to set topic type: groupId=${groupId}, topicId=${topicId}, newType=${newType}, userId=${userId}`);

    try {
      const supabase = createBotClient();

      // Find the topic
      const { data: topic } = await supabase
        .from('buddyintels_linked_topics')
        .select('*')
        .eq('group_id', groupId)
        .eq('topic_id', topicId)
        .eq('is_active', true)
        .single();

      if (!topic) {
        return { success: false, message: 'This topic is not currently linked. Use /link first.' };
      }

      // Check if user can manage the topic
      const canManage = await this.canUserManageTopic(userId, topic.linked_by_user_id);
      if (!canManage) {
        return { success: false, message: 'You do not have permission to change this topic type.' };
      }

      // Check if type is already set
      if (topic.topic_type === newType) {
        return { success: false, message: `Topic is already set to ${newType} type.` };
      }

      // Update the topic type
      const { error } = await supabase
        .from('buddyintels_linked_topics')
        .update({
          topic_type: newType,
          updated_at: new Date().toISOString()
        })
        .eq('id', topic.id);

      if (error) throw error;

      // Log activity
      await this.logActivity(topic.id, userId, username, 'type_changed', {
        old_type: topic.topic_type,
        new_type: newType
      });

      console.log(`[TopicManager] Successfully changed topic type: id=${topic.id}, from=${topic.topic_type}, to=${newType}`);
      
      const typeDescription = newType === 'logs' 
        ? 'LOGS (simple DD/MM/YYYY | Headline | Link format)'
        : 'Standard (full summary with context)';
      
      return { 
        success: true, 
        message: `Topic type changed to ${typeDescription}.` 
      };

    } catch (error) {
      console.error(`[TopicManager] Error setting topic type:`, error);
      return { success: false, message: 'An error occurred while changing the topic type.' };
    }
  }

  /**
   * Check if user can manage a specific topic
   */
  private async canUserManageTopic(userId: number, topicOwnerId: number): Promise<boolean> {
    // User can manage if they own the topic
    if (userId === topicOwnerId) {
      return true;
    }

    // Check if user has admin permissions
    try {
      const supabase = createBotClient();
      const { data: permission } = await supabase
        .from('buddyintels_topic_permissions')
        .select('*')
        .eq('user_id', userId)
        .single();

      return permission?.is_admin || permission?.can_manage_all_topics || false;
    } catch (error) {
      console.error(`[TopicManager] Error checking manage permissions:`, error);
      return false;
    }
  }
}

export const topicManager = new TopicManager();
