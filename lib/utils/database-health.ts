/**
 * Database Health Check Utilities
 * Production-ready database connection and schema validation
 */

import { createClient } from '../supabase/server.js';

export interface HealthCheckResult {
  healthy: boolean;
  error?: string;
  details?: Record<string, unknown>;
}

/**
 * Check if Supabase connection is working
 */
export async function checkDatabaseConnection(): Promise<HealthCheckResult> {
  try {
    const supabase = await createClient();
    
    // Simple query to test connection
    const { data, error } = await supabase
      .from('buddyintels_bot_config')
      .select('key, value')
      .limit(1);
    
    if (error) {
      return {
        healthy: false,
        error: error.message,
        details: { code: error.code, hint: error.hint }
      };
    }
    
    return {
      healthy: true,
      details: { configRows: data?.length || 0 }
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check if all required tables exist
 */
export async function checkRequiredTables(): Promise<HealthCheckResult> {
  const requiredTables = [
    'buddyintels_bot_config',
    'buddyintels_linked_topics',
    'buddyintels_topic_permissions',
    'buddyintels_processed_messages',
    'buddyintels_tweet_summaries',
    'buddyintels_error_logs',
    'buddyintels_topic_activity_logs'
  ];
  
  try {
    const supabase = await createClient();
    const tableStatus: Record<string, boolean> = {};
    const missingTables: string[] = [];
    
    for (const table of requiredTables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error && error.code === '42P01') {
          tableStatus[table] = false;
          missingTables.push(table);
        } else {
          tableStatus[table] = true;
        }
      } catch {
        tableStatus[table] = false;
        missingTables.push(table);
      }
    }
    
    const allTablesExist = missingTables.length === 0;
    
    return {
      healthy: allTablesExist,
      error: missingTables.length > 0 ? `Missing tables: ${missingTables.join(', ')}` : undefined,
      details: {
        tableStatus,
        missingCount: missingTables.length,
        totalCount: requiredTables.length
      }
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check if bot configuration is properly loaded
 */
export async function checkBotConfiguration(): Promise<HealthCheckResult> {
  const requiredConfigs = [
    'rate_limit_per_minute',
    'summary_cache_hours',
    'max_context_depth',
    'require_admin_for_linking',
    'auto_detect_topic_names'
  ];
  
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('buddyintels_bot_config')
      .select('key, value');
    
    if (error) {
      return {
        healthy: false,
        error: error.message,
        details: { code: error.code }
      };
    }
    
    const configMap = new Map(data?.map(item => [item.key, item.value]) || []);
    const missingConfigs = requiredConfigs.filter(key => !configMap.has(key));
    
    return {
      healthy: missingConfigs.length === 0,
      error: missingConfigs.length > 0 ? `Missing configs: ${missingConfigs.join(', ')}` : undefined,
      details: {
        totalConfigs: configMap.size,
        requiredConfigs: requiredConfigs.length,
        missingConfigs,
        configs: Object.fromEntries(configMap)
      }
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Run comprehensive health check
 */
export async function runHealthCheck(): Promise<{
  overall: boolean;
  checks: {
    connection: HealthCheckResult;
    tables: HealthCheckResult;
    configuration: HealthCheckResult;
  };
}> {
  const [connection, tables, configuration] = await Promise.all([
    checkDatabaseConnection(),
    checkRequiredTables(),
    checkBotConfiguration()
  ]);
  
  const overall = connection.healthy && tables.healthy && configuration.healthy;
  
  return {
    overall,
    checks: {
      connection,
      tables,
      configuration
    }
  };
}

/**
 * Log health check results
 */
export function logHealthCheck(results: Awaited<ReturnType<typeof runHealthCheck>>): void {
  console.log('\n🏥 Database Health Check Results:');
  console.log('================================');
  
  const { checks } = results;
  
  console.log(`🔗 Connection: ${checks.connection.healthy ? '✅' : '❌'}`);
  if (!checks.connection.healthy) {
    console.log(`   Error: ${checks.connection.error}`);
  }
  
  console.log(`📊 Tables: ${checks.tables.healthy ? '✅' : '❌'}`);
  if (!checks.tables.healthy) {
    console.log(`   Error: ${checks.tables.error}`);
    console.log(`   Missing: ${checks.tables.details?.missingCount || 0}/${checks.tables.details?.totalCount || 0} tables`);
  }
  
  console.log(`⚙️ Configuration: ${checks.configuration.healthy ? '✅' : '❌'}`);
  if (!checks.configuration.healthy) {
    console.log(`   Error: ${checks.configuration.error}`);
  }
  
  console.log(`\n🎯 Overall Status: ${results.overall ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
  
  if (!results.overall) {
    console.log('\n💡 To fix issues, run: node scripts/setup-database.js');
  }
  
  console.log('================================\n');
}