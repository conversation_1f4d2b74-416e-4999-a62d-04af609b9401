import { createClient } from '@/lib/supabase/server';

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export interface LogContext {
  user_id?: number;
  chat_id?: number;
  topic_id?: number;
  action?: string;
  component?: string;
  [key: string]: unknown;
}

export class Logger {
  private component: string;

  constructor(component: string) {
    this.component = component;
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log info message
   */
  info(message: string, context?: LogContext): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log error message
   */
  error(message: string, error?: Error, context?: LogContext): void {
    const errorContext = {
      ...context,
      error_message: error?.message,
      stack_trace: error?.stack
    };
    this.log(LogLevel.ERROR, message, errorContext);
  }

  /**
   * Log message with level
   */
  private log(level: LogLevel, message: string, context?: LogContext): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] [${this.component}] ${message}`;
    
    // Console logging
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, context);
        break;
      case LogLevel.INFO:
        console.info(logMessage, context);
        break;
      case LogLevel.WARN:
        console.warn(logMessage, context);
        break;
      case LogLevel.ERROR:
        console.error(logMessage, context);
        break;
    }

    // Database logging for errors and important events
    if (level === LogLevel.ERROR || (context?.action && ['linked', 'unlinked', 'deleted', 'activated', 'deactivated'].includes(context.action))) {
      this.logToDatabase(level, message, context).catch(err => {
        console.error('Failed to log to database:', err);
      });
    }
  }

  /**
   * Log to database
   */
  private async logToDatabase(level: LogLevel, message: string, context?: LogContext): Promise<void> {
    try {
      const supabase = await createClient();
      
      await supabase
        .from('buddyintels_error_logs')
        .insert({
          error_type: `${this.component}_${level}`,
          error_message: message,
          stack_trace: context?.stack_trace || null,
          context: {
            component: this.component,
            level: level,
            timestamp: new Date().toISOString(),
            ...context
          }
        });
    } catch (error) {
      console.error('Database logging failed:', error);
    }
  }

  /**
   * Log topic activity specifically
   */
  async logTopicActivity(
    topicId: number,
    userId: number,
    username: string | null,
    action: string,
    details?: Record<string, unknown>
  ): Promise<void> {
    try {
      const supabase = await createClient();
      
      await supabase
        .from('BuddyIntels_topic_activity_logs')
        .insert({
          topic_id: topicId,
          user_id: userId,
          username: username,
          action: action,
          details: details || {}
        });

      this.info(`Topic activity logged: ${action}`, {
        topic_id: topicId,
        user_id: userId,
        action: action
      });
    } catch (error) {
      this.error('Failed to log topic activity', error as Error, {
        topic_id: topicId,
        user_id: userId,
        action: action
      });
    }
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    userId: number,
    action: 'login' | 'logout' | 'unauthorized',
    details?: Record<string, unknown>
  ): Promise<void> {
    try {
      const supabase = await createClient();
      
      await supabase
        .from('BuddyIntels_error_logs')
        .insert({
          error_type: 'auth_event',
          error_message: `User ${userId} ${action}`,
          context: {
            component: 'auth',
            user_id: userId,
            action: action,
            timestamp: new Date().toISOString(),
            ...details
          }
        });

      this.info(`Auth event: ${action}`, {
        user_id: userId,
        action: action
      });
    } catch (error) {
      this.error('Failed to log auth event', error as Error, {
        user_id: userId,
        action: action
      });
    }
  }

  /**
   * Log API requests
   */
  logApiRequest(
    method: string,
    path: string,
    userId?: number,
    statusCode?: number,
    duration?: number
  ): void {
    const message = `${method} ${path} - ${statusCode || 'pending'}`;
    const context: LogContext = {
      component: 'api',
      method: method,
      path: path,
      status_code: statusCode,
      duration_ms: duration,
      user_id: userId
    };

    if (statusCode && statusCode >= 400) {
      this.warn(message, context);
    } else {
      this.info(message, context);
    }
  }

  /**
   * Log bot events
   */
  logBotEvent(
    event: string,
    userId: number,
    chatId: number,
    topicId?: number,
    details?: Record<string, unknown>
  ): void {
    this.info(`Bot event: ${event}`, {
      component: 'bot',
      event: event,
      user_id: userId,
      chat_id: chatId,
      topic_id: topicId,
      ...details
    });
  }
}

// Create logger instances for different components
export const botLogger = new Logger('bot');
export const apiLogger = new Logger('api');
export const authLogger = new Logger('auth');
export const dashboardLogger = new Logger('dashboard');
export const topicLogger = new Logger('topic-manager');

// Generic logger function
export function createLogger(component: string): Logger {
  return new Logger(component);
}
