/**
 * Extracts tweet ID from various Twitter/X URL formats
 */
export function extractTweetId(url: string): string | null {
  // Common Twitter URL patterns
  const patterns = [
    // Standard twitter.com URLs
    /(?:https?:\/\/)?(?:www\.)?twitter\.com\/[^\/]+\/status\/(\d+)/i,
    // X.com URLs
    /(?:https?:\/\/)?(?:www\.)?x\.com\/[^\/]+\/status\/(\d+)/i,
    // Mobile URLs
    /(?:https?:\/\/)?(?:mobile\.)?twitter\.com\/[^\/]+\/status\/(\d+)/i,
    // Shortened URLs that might redirect to Twitter
    /(?:https?:\/\/)?(?:www\.)?t\.co\/[a-zA-Z0-9]+/i,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

/**
 * Extracts all tweet URLs from a message
 */
export function extractTweetUrls(text: string): string[] {
  const urlPattern = /https?:\/\/[^\s]+/g;
  const urls = text.match(urlPattern) || [];
  
  return urls.filter(url => {
    const tweetId = extractTweetId(url);
    return tweetId !== null;
  });
}

/**
 * Validates if a string is a valid tweet ID
 */
export function isValidTweetId(id: string): boolean {
  return /^\d+$/.test(id) && id.length >= 10 && id.length <= 20;
}

/**
 * Normalizes a tweet URL to a standard format
 */
export function normalizeTweetUrl(url: string): string | null {
  const tweetId = extractTweetId(url);
  if (!tweetId) return null;
  
  return `https://twitter.com/i/status/${tweetId}`;
}

/**
 * Extracts username from tweet URL if available
 */
export function extractUsername(url: string): string | null {
  const match = url.match(/(?:https?:\/\/)?(?:www\.)?(?:twitter|x)\.com\/([^\/]+)\/status\/\d+/i);
  return match ? match[1] : null;
}