# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

```bash
bun run build                  # Build for production
bun run start                  # Start production server
bun run lint                   # Run ESLint
```

## 🛠️ Tech Stack

- **Framework**: Next.js 15+ with App Router
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with SSR
- **Styling**: Tailwind CSS + shadcn/ui components
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Themes**: next-themes with dark mode support
- **TypeScript**: Strict mode enabled
- **Font**: Geist Sans

## 📁 Project Structure

```
/
├── app/                      # Next.js App Router
│   ├── auth/                 # Authentication pages
│   ├── protected/            # Protected routes
│   ├── globals.css           # Global Tailwind styles
│   └── layout.tsx           # Root layout with theme provider
├── components/
│   ├── ui/                   # shadcn/ui components
│   ├── tutorial/             # Tutorial components
│   └── auth-*.tsx           # Authentication components
├── lib/
│   ├── supabase/            # Supabase client configurations
│   │   ├── client.ts        # Client-side Supabase client
│   │   ├── server.ts        # Server-side Supabase client
│   │   └── middleware.ts    # Middleware configuration
│   └── utils.ts             # Utility functions (cn, etc.)
├── docs/                     # Project documentation
│   └── PRD.md               # Product Requirements (Telegram bot)
└── middleware.ts            # Next.js middleware
```

## 🎨 Design System

- **shadcn/ui**: New York style variant with neutral base color
- **CSS Variables**: HSL color system using CSS custom properties
- **Dark Mode**: Class-based theming with system preference detection
- **Responsive**: Mobile-first approach with Tailwind breakpoints
- **Animations**: tailwindcss-animate for smooth transitions
- **Path Aliases**: `@/*` maps to root directory

## 🔧 Configuration

### Supabase Setup
- Uses SSR-compatible Supabase client
- Server components use `lib/supabase/server.ts`
- Client components use `lib/supabase/client.ts`
- Middleware handles auth refresh at `lib/supabase/middleware.ts`

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key
```

### TypeScript Configuration
- Strict mode enabled
- Path aliases configured (`@/*`)
- Next.js plugin for optimal bundling
- ES2017 target for broader compatibility

## 🎯 Key Features

### Authentication System
- Complete auth flow with login, signup, forgot password
- Password update functionality
- Protected routes with middleware
- SSR-compatible session management

### UI Components
- Pre-built shadcn/ui components in `/components/ui/`
- Custom auth forms with error handling
- Tutorial components for onboarding
- Theme switcher with system preference

### Project Context
- Primary goal: Telegram bot for tweet context summarization
- Uses Supabase for configuration storage
- Integrates with Twitter API and AI services
- Monorepo structure planned (see docs/PRD.md)

## 🔐 Security Considerations

- Supabase SSR package prevents auth vulnerabilities
- Environment variables for sensitive data
- Middleware refreshes user sessions
- Server-side auth validation

## 🏗️ Architecture Patterns

### Client Creation
Always create new Supabase clients within functions (avoid globals):
```typescript
// Server components
const supabase = await createClient();

// Client components  
const supabase = createClient();
```

### Component Structure
- Use TypeScript interfaces for props
- Implement proper error boundaries
- Follow Next.js App Router patterns
- Server Components by default, Client Components when needed

## 🎨 Styling Guidelines

### Tailwind Classes
- Use CSS variables for theming (`hsl(var(--primary))`)
- Leverage shadcn/ui utility classes
- Responsive design with mobile-first approach
- Consistent spacing and typography scale

### Component Styling
- Use `cn()` utility for conditional classes
- Implement proper focus states and accessibility
- Dark mode support through CSS variables
- Consistent border radius using CSS custom properties

## 📝 Development Notes

- Built from Next.js + Supabase starter template
- Uses Turbopack for faster development builds
- Geist font with `antialiased` class for better rendering
- Theme provider wraps entire app for consistent theming
- Middleware handles auth state across routes