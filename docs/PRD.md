### Project Setup Guide: Teleg<PERSON> <PERSON><PERSON> for Tweet Context Summarization

I'll guide you through building the Telegram bot as specified. The bot will monitor messages in a specific Telegram group topic. When a user sends a message containing a tweet URL (from twitter.com or x.com), the bot will:

1. Extract the tweet ID.
2. Fetch the tweet and its context (thread parents, quotes, retweets) using the provided Twitter API (https://api.twitterapi.io/twitter/tweets).
3. Use AI via Vercel AI SDK with OpenRouter and Gemini 2.5 Flash to summarize the context.
4. Reply to the user's message with the original tweet text plus the summarized context appended (e.g., "Original: [text]\n\nContext Summary: [summary]").

**Important Notes:**
- <PERSON>ts cannot edit user-sent messages per Telegram API (only their own). Instead, the bot replies with an "edited" version (original + summary). If you need deletion/editing, grant the bot admin rights to delete user messages and re-send, but that's not implemented here for simplicity.
- Assume you have:
  - A Telegram bot token (create via @BotFather).
  - OpenRouter API key (sign up at openrouter.ai).
  - TwitterAPI.io API key (sign up at twitterapi.io, get from dashboard).
  - Telegram group ID and topic ID (hardcoded in env; find via bot debugging).
- Database (Supabase PostgreSQL) is used to store configs like allowed group/topic IDs (for flexibility), but keys are in env for security.
- No frontend UI beyond Next.js defaults (as the bot is backend-focused; frontend routing is via Next.js but unused here).

#### 1. Project Structure (Turborepo Monorepo)
Use Turborepo for a monorepo. Run commands with Bun as the package manager.

```bash
bunx create-turbo@latest my-telegram-bot
cd my-telegram-bot
bun install
```

This creates:
- `apps/` (add your Next.js app here).
- `packages/` (for shared configs, e.g., ESLint).

Add Next.js app:
```bash
cd apps
bunx create-next-app@latest bot --app --ts --tailwind --eslint --no-src-dir --import-alias "@/*"
cd bot
bun install
```

Update `turbo.json` in root:
```json
{
  "pipeline": {
    "dev": {
      "cache": false
    },
    "build": {}
  }
}
```

Root `package.json` scripts:
```json
{
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build"
  }
}
```

Run dev: `bun run dev` (starts Next.js at `apps/bot`).

#### 2. Dependencies
In `apps/bot`, install:
```bash
bun add grammy @grammyjs/types @supabase/supabase-js @ai-sdk/openrouter ai zod node-fetch
bun add -D @types/node-fetch
```

- `grammy`: Telegram bot framework (works well with Next.js API routes).
- `@supabase/supabase-js`: For PostgreSQL integration.
- `@ai-sdk/openrouter` & `ai`: Vercel AI SDK for OpenRouter.
- `zod`: For AI structured output.
- `node-fetch`: For API calls (Next.js compatible).

#### 3. Environment Variables
In `apps/bot/.env.local`:
```
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
OPENROUTER_API_KEY=your-openrouter-key
TWITTER_API_KEY=your-twitterapi-io-key
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key
GROUP_ID=-1001234567890  # Your Telegram group ID (negative for groups)
TOPIC_ID=123  # Specific topic ID in the group
```

#### 4. Supabase Setup (PostgreSQL Database)
- Create a Supabase project at supabase.com.
- Create a table `configs` via SQL Editor:
  ```sql
  CREATE TABLE configs (
    id SERIAL PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL
  );
  ```
- Insert initial data (e.g., via dashboard): key=`allowed_group`, value=`-1001234567890`; key=`allowed_topic`, value=`123`.
- In code, query this for dynamic configs (instead of hardcoding).

In `apps/bot/lib/supabase.ts`:
```ts
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_ANON_KEY!);

export async function getConfig(key: string): Promise<string | null> {
  const { data, error } = await supabase.from('BuddyIntels_configs').select('value').eq('key', key).single();
  if (error) throw error;
  return data?.value ?? null;
}
```

#### 5. Twitter API Fetch Function
In `apps/bot/lib/twitter.ts`:
```ts
import fetch from 'node-fetch';

export async function fetchTweets(tweetIds: string[]): Promise<any> {
  const url = `https://api.twitterapi.io/twitter/tweets?tweet_ids=${tweetIds.join(',')}`;
  const response = await fetch(url, {
    headers: { 'X-API-Key': process.env.TWITTER_API_KEY! },
  });
  if (!response.ok) throw new Error('Twitter API error');
  const data = await response.json();
  if (data.status !== 'success') throw new Error(data.message);
  return data.tweets;
}

export async function fetchTweetContext(tweetId: string, depth = 5): Promise<any[]> {
  const context: any[] = [];
  let currentId = tweetId;
  let currentDepth = 0;

  while (currentId && currentDepth < depth) {
    const [tweet] = await fetchTweets([currentId]);
    if (!tweet) break;
    context.push(tweet);

    // Add quoted or retweeted
    if (tweet.quoted_tweet) context.push(tweet.quoted_tweet);
    if (tweet.retweeted_tweet) context.push(tweet.retweeted_tweet);

    // Traverse parent if reply
    currentId = tweet.inReplyToId || '';
    currentDepth++;
  }

  return context;
}
```

- Fetches tweet by ID, recursively gets parents (up to depth 5 to avoid loops).
- Includes quoted/retweeted tweets.

#### 6. AI Summarization
In `apps/bot/lib/ai.ts`:
```ts
import { createOpenRouter } from '@ai-sdk/openrouter';
import { generateText } from 'ai';

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY!,
  baseUrl: 'https://openrouter.ai/api/v1',
});

export async function summarizeContext(tweets: any[]): Promise<string> {
  const threadText = tweets.map(t => `${t.author.userName}: ${t.text}`).join('\n\n');
  const { text } = await generateText({
    model: openrouter('google/gemini-1.5-flash'),
    prompt: `Summarize the context of this tweet thread concisely: ${threadText}`,
  });
  return text;
}
```

- Uses Vercel AI SDK with OpenRouter provider.
- Model: `google/gemini-1.5-flash` (closest to your "2.5"; adjust if needed).

#### 7. Telegram Webhook (Next.js API Route)
In `apps/bot/app/api/telegram/route.ts`:
```ts
import { Bot, webhookCallback } from 'grammy';
import { getConfig } from '@/lib/supabase';
import { fetchTweetContext, summarizeContext } from '@/lib/twitter';

const bot = new Bot(process.env.TELEGRAM_BOT_TOKEN!);

bot.command('start', ctx => ctx.reply('Bot ready!'));

bot.on('message:text', async ctx => {
  const message = ctx.message;
  const allowedGroup = await getConfig('allowed_group');
  const allowedTopic = await getConfig('allowed_topic');

  if (String(message.chat.id) !== allowedGroup || message.message_thread_id?.toString() !== allowedTopic) return;

  // Extract tweet ID from URL (regex for twitter.com or x.com)
  const tweetUrlMatch = message.text.match(/https?:\/\/(twitter|x)\.com\/[^/]+\/status\/(\d+)/);
  if (!tweetUrlMatch) return;
  const tweetId = tweetUrlMatch[2];

  try {
    const context = await fetchTweetContext(tweetId);
    const summary = await summarizeContext(context);
    const originalTweet = context.find(t => t.id === tweetId);
    const replyText = `Original: ${originalTweet?.text}\n\nContext Summary: ${summary}`;

    await ctx.reply(replyText, { reply_to_message_id: message.message_id });
  } catch (error) {
    await ctx.reply('Error processing tweet.');
    console.error(error);
  }
});

export const POST = webhookCallback(bot, 'next-js');

// Set webhook once (run manually or on deploy): https://api.telegram.org/bot<TOKEN>/setWebhook?url=https://your-domain.com/api/telegram
```

- Handles webhook POST from Telegram.
- Filters for specific group/topic via Supabase.
- Parses tweet URL, fetches context, summarizes, replies.
- For topics: Uses `message.message_thread_id` and `is_topic_message` implicitly via filter.

#### 8. Deployment
- Deploy to Vercel: Push to GitHub, import to Vercel, add env vars.
- Set Telegram webhook: `https://api.telegram.org/bot<TOKEN>/setWebhook?url=https://your-vercel-url/api/telegram`
- Test: Send a tweet URL in the group topic; bot replies with summary.

#### 9. Potential Improvements
- Handle multiple URLs per message.
- Use Zod for structured AI output (e.g., JSON summary).
- Add error logging to Supabase.
- If needed, a simple Next.js page at `/` for admin (e.g., update configs via Supabase auth).
- Rate limits: TwitterAPI.io may have limits; add caching.

This setup uses all specified tools/tech. If issues, debug with `bun run dev`.