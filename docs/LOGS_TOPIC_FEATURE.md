# Logs Topic Feature Documentation

## Overview

The Logs Topic feature allows users to create special "logs" type topics that receive concise summaries of all tweets processed in any topic. This provides a centralized location to see all processed tweets in a compact format.

## Features

1. **Dual Delivery**:
   - Tweets are processed in their original topic with full summaries
   - The same tweets are also sent to all "logs" type topics with a concise format

2. **Concise Format**:
   - Format: `DD/MM/YYYY | Headline | Link`
   - Headlines longer than 50 characters are truncated with "..."
   - Links use emoji (🔗) to save space

3. **Permission System**:
   - Only users who own a topic or have admin permissions can change a topic's type
   - All topic type changes are logged in the activity log

## Database Changes

1. **Schema Changes**:
   ```sql
   -- Add topic_type column with default value 'standard'
   ALTER TABLE buddyintels_linked_topics 
   ADD COLUMN IF NOT EXISTS topic_type VARCHAR(20) DEFAULT 'standard' 
   CHECK (topic_type IN ('standard', 'logs'));

   -- Create index for better query performance
   CREATE INDEX IF NOT EXISTS idx_linked_topics_topic_type ON buddyintels_linked_topics(topic_type);
   ```

2. **Updated Functions**:
   - `get_active_linked_topics()` now includes the topic_type column
   - Added `update_topic_type()` function for safely changing topic types with permission checks
   - Added trigger to log topic type changes

## Usage

### Creating a Logs Topic

1. Create a new topic in a Telegram group
2. Use the `/link logs` command to link it as a logs topic
   ```
   /link logs
   ```

### Changing Topic Type

1. For an existing topic, use the `/type` command:
   ```
   /type logs
   ```
   or
   ```
   /type standard
   ```

### Viewing Logs

Once set up, the logs topic will automatically receive concise summaries of all tweets processed in any topic.

## Implementation Details

### Message Format

The logs message format is generated by the `generateLogsReplyText` method:

```typescript
async generateLogsReplyText(headline: string, tweetUrl: string): Promise<string> {
  const dateStr = this.formatDateDDMMYYYY(new Date());
  
  // Make headline more concise - limit to 50 characters
  let conciseHeadline = headline;
  if (headline.length > 50) {
    conciseHeadline = headline.substring(0, 47) + '...';
  }
  
  // Simple format: DD/MM/YYYY | Headline | Link
  return `${dateStr} | ${conciseHeadline} | [🔗](${tweetUrl})`;
}
```

### Logs Topic Distribution

When a tweet is processed, it's sent to both the original topic and all logs topics:

```typescript
// Send to current topic
await this.sendMessageWithRetry(ctx, message.chat.id, replyText, {
  parse_mode: 'Markdown',
  message_thread_id: message.message_thread_id
});

// Also send to logs topics if current topic is not already logs type
if (!isLogsType) {
  await this.sendToLogsTopics(summary.headline, tweetUrl);
}
```

## Testing

The feature has been tested with:

1. **Database Tests**:
   - Verified column addition
   - Tested constraint enforcement
   - Checked index creation

2. **Functionality Tests**:
   - Tested topic type retrieval
   - Verified message formatting
   - Confirmed truncation logic
   - Validated logs topic distribution

## Maintenance

To monitor the logs topic feature:

1. Check the `buddyintels_topic_activity_logs` table for topic type changes
2. Monitor the server logs for any errors in the `sendToLogsTopics` method
3. Use the test scripts in the `scripts/` directory to verify functionality
