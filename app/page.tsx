import { createClient } from "@/lib/supabase/server";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default async function Home() {
  const supabase = await createClient();
  
  // Simple health check
  const { data: healthCheck } = await supabase.from('BuddyIntels_bot_config').select('count').limit(1).maybeSingle();
  
  return (
    <main className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="text-center space-y-6">
        <div className="flex flex-col items-center space-y-4">
          <Image
            src="/logo.jpg"
            alt="BuddyIntels Logo"
            width={120}
            height={120}
            className="rounded-full shadow-lg"
            priority
          />
          <h1 className="text-4xl font-bold text-foreground">
            BuddyIntels
          </h1>
        </div>
        <p className="text-muted-foreground max-w-md">
          AI-Powered Twitter Context Bot for Telegram
        </p>
        <p className="text-sm text-muted-foreground max-w-lg">
          Your intelligent companion for comprehensive Twitter thread analysis and context summarization using advanced AI
        </p>
        <div className="mt-8 p-6 bg-muted rounded-lg border">
          <h2 className="text-lg font-semibold mb-4">🤖 Bot Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <span>Database:</span>
              <span className={healthCheck !== undefined ? "text-green-600" : "text-red-600"}>
                {healthCheck !== undefined ? "✅ Connected" : "❌ Error"}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span>Webhook:</span>
              <span className="text-green-600">✅ Ready</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>AI Engine:</span>
              <span className="text-green-600">✅ Active</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>Twitter API:</span>
              <span className="text-green-600">✅ Connected</span>
            </div>
          </div>
        </div>

        <div className="mt-6 flex flex-col sm:flex-row gap-4">
          <Link href="/dashboard">
            <Button size="lg" className="w-full sm:w-auto">
              📊 Topic Management
            </Button>
          </Link>
          <Link href="/analytics">
            <Button size="lg" variant="outline" className="w-full sm:w-auto">
              📈 Analytics Dashboard
            </Button>
          </Link>
        </div>

        <div className="mt-8 text-xs text-muted-foreground">
          <p>🔒 Secure • 🚀 Fast • 🤖 AI-Powered</p>
        </div>
      </div>
    </main>
  );
}