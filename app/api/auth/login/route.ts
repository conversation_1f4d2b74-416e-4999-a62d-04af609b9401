import { NextRequest, NextResponse } from 'next/server';
import { authManager } from '@/lib/auth/auth';

// POST /api/auth/login - Authenticate user
export async function POST(request: NextRequest) {
  console.log('[API] POST /api/auth/login - User login attempt');
  
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId || typeof userId !== 'number') {
      return NextResponse.json(
        { error: 'Valid userId is required' },
        { status: 400 }
      );
    }

    console.log(`[API] Login attempt for user ${userId}`);

    // Generate token and authenticate
    const token = authManager.generateUserToken(userId);
    const user = await authManager.authenticateUser(userId, token);

    if (!user) {
      console.log(`[API] Authentication failed for user ${userId}`);
      return NextResponse.json(
        { 
          error: 'Authentication failed. You may not have permission to access this dashboard. Please ensure you have used the bot at least once or contact an administrator.' 
        },
        { status: 401 }
      );
    }

    // Set authentication cookies
    await authManager.setAuthCookie(userId);

    console.log(`[API] Successfully authenticated user ${userId}`);
    
    return NextResponse.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        username: user.username,
        first_name: user.first_name,
        is_admin: user.is_admin,
        can_link_topics: user.can_link_topics,
        can_manage_all_topics: user.can_manage_all_topics
      }
    });

  } catch (error) {
    console.error('[API] Unexpected error in POST /api/auth/login:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/auth/login - Check current authentication status
export async function GET() {
  console.log('[API] GET /api/auth/login - Check auth status');
  
  try {
    const user = await authManager.getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { authenticated: false },
        { status: 401 }
      );
    }

    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        username: user.username,
        first_name: user.first_name,
        is_admin: user.is_admin,
        can_link_topics: user.can_link_topics,
        can_manage_all_topics: user.can_manage_all_topics
      }
    });

  } catch (error) {
    console.error('[API] Unexpected error in GET /api/auth/login:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
