import { NextResponse } from 'next/server';
import { authManager } from '@/lib/auth/auth';

// POST /api/auth/logout - Logout user
export async function POST() {
  console.log('[API] POST /api/auth/logout - User logout');
  
  try {
    await authManager.clearAuthCookies();
    
    console.log('[API] User logged out successfully');
    
    return NextResponse.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('[API] Unexpected error in POST /api/auth/logout:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
