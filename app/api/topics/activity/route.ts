import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// GET /api/topics/activity - Get topic activity logs
export async function GET(request: NextRequest) {
  console.log('[API] GET /api/topics/activity - Fetching topic activity logs');
  
  try {
    const { searchParams } = new URL(request.url);
    const topicId = searchParams.get('topicId');
    const userId = searchParams.get('userId');
    const action = searchParams.get('action');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const supabase = await createClient();
    
    let query = supabase
      .from('buddyintels_topic_activity_logs')
      .select(`
        *,
        buddyintels_linked_topics(
          id,
          group_title,
          topic_title,
          group_id,
          topic_id
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (topicId) {
      query = query.eq('topic_id', parseInt(topicId));
    }

    if (userId) {
      query = query.eq('user_id', parseInt(userId));
    }

    if (action) {
      query = query.eq('action', action);
    }

    const { data: activities, error, count } = await query;

    if (error) {
      console.error('[API] Error fetching activity logs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch activity logs' },
        { status: 500 }
      );
    }

    console.log(`[API] Successfully fetched ${activities?.length || 0} activity logs`);
    
    return NextResponse.json({
      success: true,
      data: activities || [],
      count: activities?.length || 0,
      total: count,
      pagination: {
        limit,
        offset,
        hasMore: (activities?.length || 0) === limit
      }
    });

  } catch (error) {
    console.error('[API] Unexpected error in GET /api/topics/activity:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/topics/activity - Create a new activity log entry (for manual logging)
export async function POST(request: NextRequest) {
  console.log('[API] POST /api/topics/activity - Creating activity log entry');
  
  try {
    const body = await request.json();
    const {
      topic_id,
      user_id,
      username,
      action,
      details
    } = body;

    // Validate required fields
    if (!topic_id || !user_id || !action) {
      return NextResponse.json(
        { error: 'topic_id, user_id, and action are required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verify topic exists
    const { error: topicError } = await supabase
      .from('buddyintels_linked_topics')
      .select('id')
      .eq('id', topic_id)
      .single();

    if (topicError) {
      if (topicError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Topic not found' },
          { status: 404 }
        );
      }
      
      console.error('[API] Error verifying topic:', topicError);
      return NextResponse.json(
        { error: 'Failed to verify topic' },
        { status: 500 }
      );
    }

    // Create activity log entry
    const { data: activity, error } = await supabase
      .from('BuddyIntels_topic_activity_logs')
      .insert({
        topic_id,
        user_id,
        username,
        action,
        details: details || {}
      })
      .select()
      .single();

    if (error) {
      console.error('[API] Error creating activity log:', error);
      return NextResponse.json(
        { error: 'Failed to create activity log' },
        { status: 500 }
      );
    }

    console.log('[API] Successfully created activity log entry');
    
    return NextResponse.json({
      success: true,
      message: 'Activity log created successfully',
      data: activity
    });

  } catch (error) {
    console.error('[API] Unexpected error in POST /api/topics/activity:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

