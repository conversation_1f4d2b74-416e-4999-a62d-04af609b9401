import { Metadata } from "next";
import { LoginForm } from "@/components/auth/login-form";
import { authManager } from "@/lib/auth/auth";
import { redirect } from "next/navigation";
import Image from "next/image";

export const metadata: Metadata = {
  title: "Login - BuddyIntels Dashboard",
  description: "Login to access the BuddyIntels topic management dashboard",
};

export default async function LoginPage() {
  // Check if user is already authenticated
  const currentUser = await authManager.getCurrentUser();
  if (currentUser) {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Image
            src="/logo.jpg"
            alt="BuddyIntels Logo"
            width={80}
            height={80}
            className="mx-auto rounded-full shadow-lg"
            priority
          />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            BuddyIntels Dashboard
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Sign in to manage your Telegram topics and analytics
          </p>
        </div>
        <LoginForm />
      </div>
    </div>
  );
}
