import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import "./globals.css";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL(defaultUrl),
  title: "BuddyIntels - AI-Powered Twitter Context Bot",
  description: "BuddyIntels: Your intelligent Telegram bot for comprehensive Twitter thread analysis and context summarization using advanced AI",
  keywords: ["telegram bot", "twitter analysis", "AI summarization", "thread context", "social media intelligence"],
  authors: [{ name: "BuddyIntels Team" }],
  creator: "BuddyIntels",
  publisher: "BuddyIntels",
  robots: "index, follow",
  openGraph: {
    title: "BuddyIntels - AI-Powered Twitter Context Bot",
    description: "Your intelligent Telegram bot for comprehensive Twitter thread analysis and context summarization",
    url: defaultUrl,
    siteName: "BuddyIntels",
    images: [
      {
        url: "/logo.jpg",
        width: 1200,
        height: 630,
        alt: "BuddyIntels Logo",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "BuddyIntels - AI-Powered Twitter Context Bot",
    description: "Your intelligent Telegram bot for comprehensive Twitter thread analysis and context summarization",
    images: ["/logo.jpg"],
    creator: "@BuddyIntels",
  },
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/icon-192x192.png", sizes: "192x192", type: "image/png" },
    ],
    other: [
      { rel: "icon", url: "/icon-192x192.png", sizes: "192x192" },
      { rel: "icon", url: "/icon-512x512.png", sizes: "512x512" },
    ],
  },
  manifest: "/manifest.json",
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
