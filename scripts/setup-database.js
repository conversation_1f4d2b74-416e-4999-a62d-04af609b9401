#!/usr/bin/env node
/**
 * Database Setup Script
 * Verifies connection and creates all required tables
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { config } from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local
config({ path: join(__dirname, '../.env.local') });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_KEY = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('❌ Missing Supabase environment variables');
  console.error('NEXT_PUBLIC_SUPABASE_URL:', SUPABASE_URL ? '✅' : '❌');
  console.error('NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY:', SUPABASE_KEY ? '✅' : '❌');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function testConnection() {
  console.log('🔗 Testing Supabase connection...');
  
  try {
    const { data, error } = await supabase
      .from('nonexistent_table')
      .select('*')
      .limit(1);
    
    // We expect an error here, but it should be "table doesn't exist", not auth error
    if (error && error.code === '42P01') {
      console.log('✅ Supabase connection successful');
      return true;
    } else if (error) {
      console.error('❌ Supabase connection failed:', error);
      return false;
    }
  } catch (err) {
    console.error('❌ Supabase connection failed:', err);
    return false;
  }
}

async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error && error.code === '42P01') {
      return false; // Table doesn't exist
    }
    return true; // Table exists
  } catch (err) {
    return false;
  }
}

async function createTables() {
  console.log('📊 Creating database tables...');
  
  const schemaPath = join(__dirname, '../supabase/schema.sql');
  const schema = readFileSync(schemaPath, 'utf8');
  
  try {
    const { data, error } = await supabase.rpc('sql', { query: schema });
    
    if (error) {
      console.error('❌ Failed to create tables:', error);
      return false;
    }
    
    console.log('✅ Database tables created successfully');
    return true;
  } catch (err) {
    // Try alternative approach - execute schema directly
    console.log('🔄 Trying alternative table creation...');
    
    const statements = schema
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0);
    
    for (const statement of statements) {
      if (statement.includes('CREATE TABLE') || statement.includes('INSERT INTO')) {
        try {
          await supabase.rpc('sql', { query: statement });
        } catch (statementError) {
          console.warn('⚠️ Statement failed (might be expected):', statement.substring(0, 50) + '...');
        }
      }
    }
    
    return true;
  }
}

async function verifyTables() {
  console.log('🔍 Verifying required tables...');
  
  const requiredTables = [
    'buddyintels_bot_config',
    'buddyintels_linked_topics',
    'buddyintels_topic_permissions',
    'buddyintels_processed_messages',
    'buddyintels_tweet_summaries',
    'buddyintels_error_logs',
    'buddyintels_topic_activity_logs'
  ];
  
  const results = {};
  
  for (const table of requiredTables) {
    const exists = await checkTableExists(table);
    results[table] = exists;
    console.log(`${exists ? '✅' : '❌'} ${table}`);
  }
  
  const allExist = Object.values(results).every(exists => exists);
  
  if (allExist) {
    console.log('🎉 All required tables exist!');
  } else {
    console.log('⚠️ Some tables are missing. Check your database setup.');
  }
  
  return allExist;
}

async function insertInitialConfig() {
  console.log('⚙️ Inserting initial configuration...');
  
  const configs = [
    { key: 'rate_limit_per_minute', value: '5' },
    { key: 'summary_cache_hours', value: '24' },
    { key: 'max_context_depth', value: '5' },
    { key: 'require_admin_for_linking', value: 'false' },
    { key: 'auto_detect_topic_names', value: 'true' }
  ];
  
  for (const config of configs) {
    try {
      const { error } = await supabase
        .from('buddyintels_bot_config')
        .upsert(config);
      
      if (error) {
        console.warn(`⚠️ Config ${config.key}:`, error.message);
      } else {
        console.log(`✅ Config ${config.key}: ${config.value}`);
      }
    } catch (err) {
      console.warn(`⚠️ Config ${config.key}:`, err.message);
    }
  }
}

async function main() {
  console.log('🚀 BuddyIntels Database Setup\n');
  
  // Test connection
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.error('❌ Cannot proceed without database connection');
    process.exit(1);
  }
  
  console.log('');
  
  // Check existing tables
  await verifyTables();
  
  console.log('');
  
  // Create tables
  await createTables();
  
  console.log('');
  
  // Verify again
  const allTablesExist = await verifyTables();
  
  if (allTablesExist) {
    console.log('');
    await insertInitialConfig();
    
    console.log('\n🎉 Database setup complete! Your bot should now work properly.');
  } else {
    console.log('\n❌ Database setup incomplete. Please check the errors above.');
    process.exit(1);
  }
}

main().catch(console.error);