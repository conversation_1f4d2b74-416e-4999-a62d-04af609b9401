#!/usr/bin/env node

/**
 * Setup script to configure Telegram webhook for the bot
 * This script will set the webhook URL for your Telegram bot
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
  } catch (error) {
    console.warn('Warning: Could not load .env.local file');
  }
}

loadEnvFile();

const BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const WEBHOOK_URL = process.env.NEXT_PUBLIC_APP_URL;
const WEBHOOK_SECRET = process.env.TELEGRAM_WEBHOOK_SECRET || '';

if (!BOT_TOKEN) {
  console.error('❌ TELEGRAM_BOT_TOKEN not found in environment variables');
  process.exit(1);
}

if (!WEBHOOK_URL) {
  console.error('❌ NEXT_PUBLIC_APP_URL not found in environment variables');
  process.exit(1);
}

const WEBHOOK_ENDPOINT = `${WEBHOOK_URL}/api/telegram/webhook`;

async function makeRequest(url, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      method: data ? 'POST' : 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve(parsed);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${responseData}`));
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function getCurrentWebhook() {
  const url = `https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo`;
  return makeRequest(url);
}

async function setWebhook() {
  const params = new URLSearchParams({
    url: WEBHOOK_ENDPOINT
  });

  if (WEBHOOK_SECRET) {
    params.append('secret_token', WEBHOOK_SECRET);
  }

  // Set allowed updates to only receive message updates
  params.append('allowed_updates', JSON.stringify(['message']));

  const url = `https://api.telegram.org/bot${BOT_TOKEN}/setWebhook?${params.toString()}`;
  return makeRequest(url);
}

async function deleteWebhook() {
  const url = `https://api.telegram.org/bot${BOT_TOKEN}/deleteWebhook`;
  return makeRequest(url);
}

async function testWebhookEndpoint() {
  return new Promise((resolve, reject) => {
    const url = `${WEBHOOK_URL}/api/telegram/webhook`;
    
    https.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data
        });
      });
    }).on('error', reject);
  });
}

async function main() {
  console.log('🤖 BuddyIntels Telegram Bot Webhook Setup\n');
  
  try {
    // Test if the webhook endpoint is accessible
    console.log('🔍 Testing webhook endpoint accessibility...');
    try {
      const endpointTest = await testWebhookEndpoint();
      console.log(`✅ Webhook endpoint is accessible (Status: ${endpointTest.statusCode})`);
    } catch (error) {
      console.log(`⚠️  Warning: Could not test webhook endpoint: ${error.message}`);
      console.log('   This is normal if the server is not running yet.');
    }

    // Get current webhook info
    console.log('\n📡 Checking current webhook configuration...');
    const currentWebhook = await getCurrentWebhook();
    
    if (currentWebhook.ok) {
      const info = currentWebhook.result;
      if (info.url) {
        console.log(`Current webhook URL: ${info.url}`);
        console.log(`Pending updates: ${info.pending_update_count}`);
        console.log(`Last error: ${info.last_error_message || 'None'}`);
        
        if (info.url === WEBHOOK_ENDPOINT) {
          console.log('✅ Webhook is already configured correctly!');
          return;
        }
      } else {
        console.log('No webhook currently configured.');
      }
    }

    // Set new webhook
    console.log(`\n🔧 Setting webhook to: ${WEBHOOK_ENDPOINT}`);
    const result = await setWebhook();
    
    if (result.ok) {
      console.log('✅ Webhook configured successfully!');
      
      // Verify the setup
      console.log('\n🔍 Verifying webhook configuration...');
      const verification = await getCurrentWebhook();
      
      if (verification.ok && verification.result.url === WEBHOOK_ENDPOINT) {
        console.log('✅ Webhook verification successful!');
        console.log('\n📋 Setup Summary:');
        console.log(`   • Bot Token: ${BOT_TOKEN.substring(0, 10)}...`);
        console.log(`   • Webhook URL: ${WEBHOOK_ENDPOINT}`);
        console.log(`   • Secret Token: ${WEBHOOK_SECRET ? 'Configured' : 'Not set'}`);
        console.log(`   • Allowed Updates: Messages only`);
        
        console.log('\n🎉 Your Telegram bot is now ready to receive webhooks!');
        console.log('\n📝 Next steps:');
        console.log('   1. Deploy your application to the production server');
        console.log('   2. Test the bot by sending a message in Telegram');
        console.log('   3. Monitor the logs for any issues');
        
      } else {
        console.error('❌ Webhook verification failed');
      }
    } else {
      console.error('❌ Failed to set webhook:');
      console.error(result);
    }
    
  } catch (error) {
    console.error('❌ Error during webhook setup:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--delete') || args.includes('-d')) {
  deleteWebhook().then(result => {
    if (result.ok) {
      console.log('✅ Webhook deleted successfully');
    } else {
      console.error('❌ Failed to delete webhook:', result);
    }
  }).catch(console.error);
} else if (args.includes('--info') || args.includes('-i')) {
  getCurrentWebhook().then(result => {
    if (result.ok) {
      console.log('📡 Current webhook info:');
      console.log(JSON.stringify(result.result, null, 2));
    } else {
      console.error('❌ Failed to get webhook info:', result);
    }
  }).catch(console.error);
} else {
  main();
}