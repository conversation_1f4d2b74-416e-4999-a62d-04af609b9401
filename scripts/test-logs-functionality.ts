#!/usr/bin/env bun

/**
 * Test script to verify logs functionality
 * This script tests the new logs topic functionality
 */

import { createBotClient } from '../lib/supabase/bot-client';
import { aiSummarizer } from '../lib/ai/summarizer';

async function testLogsFunctionality() {
  console.log('🧪 Testing logs functionality...\n');

  try {
    // Test 1: Check if we can fetch logs topics
    console.log('1. Testing logs topics query...');
    const supabase = createBotClient();
    const { data: logsTopics, error } = await supabase
      .from('buddyintels_linked_topics')
      .select('*')
      .eq('topic_type', 'logs')
      .eq('is_active', true);
    
    if (error) {
      console.error('❌ Error fetching logs topics:', error);
      return;
    }
    
    console.log(`✅ Found ${logsTopics?.length || 0} active logs topics`);
    if (logsTopics && logsTopics.length > 0) {
      logsTopics.forEach(topic => {
        console.log(`   - ${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''} (ID: ${topic.id})`);
      });
    }
    console.log();

    // Test 2: Test logs message formatting
    console.log('2. Testing logs message formatting...');
    const testHeadline = 'This is a very long headline that should be truncated to test the concise formatting functionality';
    const testUrl = 'https://twitter.com/example/status/1234567890';
    
    const logsMessage = await aiSummarizer.generateLogsReplyText(testHeadline, testUrl);
    console.log('✅ Generated logs message:');
    console.log(`   "${logsMessage}"`);
    console.log();

    // Test 3: Test with short headline
    console.log('3. Testing with short headline...');
    const shortHeadline = 'Short headline';
    const shortLogsMessage = await aiSummarizer.generateLogsReplyText(shortHeadline, testUrl);
    console.log('✅ Generated short logs message:');
    console.log(`   "${shortLogsMessage}"`);
    console.log();

    // Test 4: Test database function for updating topic type
    console.log('4. Testing topic type update function...');
    const { data: testResult, error: funcError } = await supabase
      .rpc('update_topic_type', {
        topic_id_param: 99999, // Non-existent topic
        new_type_param: 'logs',
        user_id_param: 904041730,
        username_param: 'test_user'
      });
    
    if (funcError) {
      console.error('❌ Function error:', funcError);
    } else {
      console.log('✅ Function response:', testResult);
    }
    console.log();

    console.log('🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testLogsFunctionality().catch(console.error);
