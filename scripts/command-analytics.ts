#!/usr/bin/env bun
/**
 * Command Analytics Management Script
 * 
 * This script provides utilities to analyze and manage bot command usage data.
 * 
 * Usage:
 *   bun run scripts/command-analytics.ts [command] [options]
 * 
 * Commands:
 *   stats [days]           - Show command usage statistics
 *   users [limit] [days]   - Show top users by activity
 *   chats [limit]          - Show most active chats
 *   errors [days]          - Show error analysis
 *   cleanup [days]         - Clean up old data
 *   export [days]          - Export data to CSV
 *   dashboard              - Show comprehensive dashboard
 */

import { createClient } from '@/lib/supabase/server';
import { writeFileSync } from 'fs';
import { join } from 'path';

interface CommandStat {
  command: string;
  total_uses: number;
  unique_users: number;
  unique_chats: number;
  avg_response_time_ms: number;
  error_rate: number;
  rate_limit_rate: number;
}

interface UserActivity {
  user_id: number;
  username: string;
  first_name: string;
  total_commands: number;
  unique_commands: number;
  last_activity: string;
}

interface ChatActivity {
  chat_id: number;
  chat_type: string;
  chat_title: string;
  total_commands: number;
  unique_users: number;
  unique_commands: number;
  avg_response_time: number;
  error_count: number;
  first_activity: string;
  last_activity: string;
}

class CommandAnalytics {
  private supabase: any;

  constructor() {
    this.initSupabase();
  }

  private async initSupabase() {
    this.supabase = await createClient();
  }

  /**
   * Get command usage statistics
   */
  async getCommandStats(days: number = 7): Promise<CommandStat[]> {
    const { data, error } = await this.supabase
      .rpc('get_command_stats', {
        start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        end_date: new Date().toISOString()
      });

    if (error) {
      console.error('Error fetching command stats:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get top users by activity
   */
  async getTopUsers(limit: number = 10, days: number = 30): Promise<UserActivity[]> {
    const { data, error } = await this.supabase
      .rpc('get_top_users', {
        limit_count: limit,
        start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
      });

    if (error) {
      console.error('Error fetching top users:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get chat activity statistics
   */
  async getChatActivity(limit: number = 10): Promise<ChatActivity[]> {
    const { data, error } = await this.supabase
      .from('BuddyIntels_chat_activity')
      .select('*')
      .order('total_commands', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching chat activity:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get error analysis
   */
  async getErrorAnalysis(days: number = 7): Promise<any[]> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await this.supabase
      .from('BuddyIntels_command_usage')
      .select('command, error_message, COUNT(*) as error_count')
      .eq('success', false)
      .gte('created_at', startDate)
      .group('command, error_message')
      .order('error_count', { ascending: false });

    if (error) {
      console.error('Error fetching error analysis:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get daily usage trends
   */
  async getDailyTrends(days: number = 30): Promise<any[]> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await this.supabase
      .from('BuddyIntels_command_usage')
      .select(`
        DATE_TRUNC('day', created_at) as date,
        command,
        COUNT(*) as count,
        COUNT(DISTINCT user_id) as unique_users,
        AVG(response_time_ms) as avg_response_time
      `)
      .gte('created_at', startDate)
      .group('DATE_TRUNC(\'day\', created_at), command')
      .order('date', { ascending: true });

    if (error) {
      console.error('Error fetching daily trends:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Clean up old command usage data
   */
  async cleanupOldData(daysToKeep: number = 90): Promise<number> {
    const { data, error } = await this.supabase
      .rpc('cleanup_old_command_usage', {
        days_to_keep: daysToKeep
      });

    if (error) {
      console.error('Error cleaning up old data:', error);
      return 0;
    }

    return data || 0;
  }

  /**
   * Export data to CSV
   */
  async exportToCSV(days: number = 30): Promise<string> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await this.supabase
      .from('BuddyIntels_command_usage')
      .select('*')
      .gte('created_at', startDate)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error exporting data:', error);
      return '';
    }

    if (!data || data.length === 0) {
      console.log('No data to export');
      return '';
    }

    // Convert to CSV
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map((row: Record<string, unknown>) => 
      Object.values(row).map((value: unknown) => 
        typeof value === 'string' && value.includes(',') 
          ? `"${value.replace(/"/g, '""')}"` 
          : value
      ).join(',')
    );

    const csv = [headers, ...rows].join('\n');
    
    // Save to file
    const filename = `command-usage-${new Date().toISOString().split('T')[0]}.csv`;
    const filepath = join(process.cwd(), 'exports', filename);
    
    try {
      writeFileSync(filepath, csv);
      console.log(`✅ Data exported to: ${filepath}`);
      return filepath;
    } catch (writeError) {
      console.error('Error writing CSV file:', writeError);
      return '';
    }
  }

  /**
   * Display comprehensive dashboard
   */
  async showDashboard(): Promise<void> {
    console.log('\n🚀 BuddyIntels Command Analytics Dashboard\n');
    console.log('=' .repeat(60));

    // Command Statistics (Last 7 days)
    console.log('\n📊 Command Usage (Last 7 days):');
    console.log('-'.repeat(40));
    const stats = await this.getCommandStats(7);
    if (stats.length > 0) {
      console.table(stats.map(s => ({
        Command: s.command,
        'Total Uses': s.total_uses,
        'Unique Users': s.unique_users,
        'Avg Response (ms)': Math.round(s.avg_response_time_ms || 0),
        'Error Rate %': (s.error_rate || 0).toFixed(1),
        'Rate Limited %': (s.rate_limit_rate || 0).toFixed(1)
      })));
    } else {
      console.log('No command data available');
    }

    // Top Users (Last 30 days)
    console.log('\n👥 Top Users (Last 30 days):');
    console.log('-'.repeat(40));
    const users = await this.getTopUsers(10, 30);
    if (users.length > 0) {
      console.table(users.map(u => ({
        'User ID': u.user_id,
        'Username': u.username || 'N/A',
        'First Name': u.first_name || 'N/A',
        'Total Commands': u.total_commands,
        'Unique Commands': u.unique_commands,
        'Last Activity': new Date(u.last_activity).toLocaleDateString()
      })));
    } else {
      console.log('No user data available');
    }

    // Most Active Chats
    console.log('\n💬 Most Active Chats:');
    console.log('-'.repeat(40));
    const chats = await this.getChatActivity(10);
    if (chats.length > 0) {
      console.table(chats.map(c => ({
        'Chat ID': c.chat_id,
        'Type': c.chat_type,
        'Title': c.chat_title || 'N/A',
        'Total Commands': c.total_commands,
        'Unique Users': c.unique_users,
        'Avg Response (ms)': Math.round(c.avg_response_time || 0),
        'Errors': c.error_count
      })));
    } else {
      console.log('No chat data available');
    }

    // Error Analysis (Last 7 days)
    console.log('\n🚨 Error Analysis (Last 7 days):');
    console.log('-'.repeat(40));
    const errors = await this.getErrorAnalysis(7);
    if (errors.length > 0) {
      console.table(errors.slice(0, 10).map(e => ({
        'Command': e.command,
        'Error Message': e.error_message?.substring(0, 50) + '...' || 'N/A',
        'Count': e.error_count
      })));
    } else {
      console.log('No errors in the last 7 days! 🎉');
    }

    console.log('\n' + '='.repeat(60));
    console.log('Dashboard complete! Use specific commands for detailed analysis.');
  }
}

// CLI Interface
async function main() {
  const analytics = new CommandAnalytics();
  const args = process.argv.slice(2);
  const command = args[0] || 'dashboard';

  try {
    switch (command) {
      case 'stats': {
        const days = parseInt(args[1]) || 7;
        console.log(`\n📊 Command Statistics (Last ${days} days):`);
        const stats = await analytics.getCommandStats(days);
        console.table(stats);
        break;
      }

      case 'users': {
        const limit = parseInt(args[1]) || 10;
        const days = parseInt(args[2]) || 30;
        console.log(`\n👥 Top ${limit} Users (Last ${days} days):`);
        const users = await analytics.getTopUsers(limit, days);
        console.table(users);
        break;
      }

      case 'chats': {
        const limit = parseInt(args[1]) || 10;
        console.log(`\n💬 Top ${limit} Most Active Chats:`);
        const chats = await analytics.getChatActivity(limit);
        console.table(chats);
        break;
      }

      case 'errors': {
        const days = parseInt(args[1]) || 7;
        console.log(`\n🚨 Error Analysis (Last ${days} days):`);
        const errors = await analytics.getErrorAnalysis(days);
        console.table(errors);
        break;
      }

      case 'cleanup': {
        const daysToKeep = parseInt(args[1]) || 90;
        console.log(`\n🧹 Cleaning up data older than ${daysToKeep} days...`);
        const deleted = await analytics.cleanupOldData(daysToKeep);
        console.log(`✅ Deleted ${deleted} old records`);
        break;
      }

      case 'export': {
        const days = parseInt(args[1]) || 30;
        console.log(`\n📤 Exporting data from last ${days} days...`);
        const filepath = await analytics.exportToCSV(days);
        if (filepath) {
          console.log(`✅ Export complete: ${filepath}`);
        }
        break;
      }

      case 'dashboard':
      default: {
        await analytics.showDashboard();
        break;
      }
    }
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run if called directly (Node.js check)
if (typeof require !== 'undefined' && require.main === module) {
  main();
}
